// File type badge
.file-type-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.9rem;
  text-align: center;
  min-width: 60px;
}

// Background colors
.bg-light {
  &-danger {
    background-color: rgba(241, 65, 108, 0.1);
  }
  &-primary {
    background-color: rgba(0, 163, 255, 0.1);
  }
  &-success {
    background-color: rgba(80, 205, 137, 0.1);
  }
  &-warning {
    background-color: rgba(255, 199, 0, 0.1);
  }
  &-info {
    background-color: rgba(7, 156, 236, 0.1);
  }
  &-secondary {
    background-color: rgba(181, 181, 195, 0.1);
  }
}

// Helper classes
.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.w-100px {
  width: 100px;
}

// Card styles
.card {
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15) !important;
  }
}

.card-body {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// Image styles
.img-fluid {
  object-fit: cover;
  width: 100%;
  height: 120px;
  border-radius: 4px;
}

.cursor-pointer {
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.05);
  }
}

// Gallery modal
::ng-deep .image-gallery-modal {
  .modal-dialog {
    max-width: 60%;
    margin: 1.75rem auto;
  }

  .modal-content {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    min-height: 80vh;
  }

  .modal-body {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
  }
}

.gallery-image {
  max-width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
  margin: 0 auto;
}

// Navigation buttons
.btn-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0.8;
  transition: opacity 0.3s, transform 0.3s;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
}
