<div class="register-container">
  <!-- Registration Form Card -->
  <div class="register-card">
    <!-- Step 1: Login Form -->
    <div *ngIf="currentStep === 1" class="registration-stepper-step">
      <div class="login-stepper">
        <div class="step-content">
          <h1 class="step-title">Login to your account</h1>

          <form [formGroup]="loginForm" (ngSubmit)="login()">
            <!-- phone -->
            <div class="form-group">
              <label for="phone" class="form-label">
                <i class="ki-outline ki-user"></i>
                Phone <span class="required"></span>
              </label>
              <input type="tel" id="phone" formControlName="phone" class="form-control"
                [class.is-invalid]="isFieldInvalid('phone')" placeholder="01xxxxxxxxx" required autocomplete="tel"
                (blur)="markFieldAsTouched('phone')" />
              <div *ngIf="isFieldInvalid('phone')" class="invalid-feedback">
                {{ getFieldError("phone") }}
              </div>
            </div>

            <!-- Password -->
            <div class="form-group">
              <label for="password" class="form-label">
                <i class="ki-outline ki-lock"></i>
                Password <span class="required"></span>
              </label>
              <input type="password" id="password" formControlName="password" class="form-control"
                [class.is-invalid]="isFieldInvalid('password')" placeholder="Enter your password" required
                autocomplete="current-password" (blur)="markFieldAsTouched('password')" />
              <div *ngIf="isFieldInvalid('password')" class="invalid-feedback">
                {{ getFieldError("password") }}
              </div>
            </div>

            <!-- Forgot Password & Remember Me Row -->
            <div class="form-row">
              <!-- Forgot Password Link (Left) -->
              <div class="forgot-password-link">
                <a (click)="goToForgotPassword()" class="forgot-link" style="cursor: pointer">Forgot your password?</a>
              </div>

              <!-- Remember Me (Right) -->
              <div class="form-check">
                <input type="checkbox" id="rememberMe" formControlName="rememberMe" class="form-check-input" />
                <label for="rememberMe" class="form-check-label">
                  Remember me
                </label>
              </div>
            </div>

            <!-- Login Error Message -->
            <div *ngIf="loginErrorMessage" class="alert alert-danger mt-3" role="alert">
              {{ loginErrorMessage }}
            </div>

            <!-- Login Button -->
            <button type="submit" class="btn btn-primary btn-verification" [class.loading]="isLoadingLogin"
              [disabled]="!isLoginFormValid() || isLoadingLogin" (click)="login()">
              <span *ngIf="isLoadingLogin" class="spinner-border spinner-border-sm me-2" role="status"></span>
              {{ isLoadingLogin ? "Logging in..." : "Log In" }}
            </button>
          </form>

          <!-- Help Text -->
          <div class="help-text register-section">
            <div class="register-text">
              <a routerLink="../register" class="register-link">
                <span class="register-message">Don't have an account?
                  <span class="create-link">Create one</span></span>
                <i class="ki-outline ki-arrow-right"></i>
              </a>
            </div>
          </div>

          <div class="help-text">
            Need help? <span class="contact-link">Contact us</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 2: Forgot Password Form -->
    <div *ngIf="currentStep === 2" class="registration-stepper-step">
      <div class="login-stepper">
        <div class="step-content">
          <h1 class="step-title">Forgot Password</h1>
          <p class="step-subtitle">
            Don't worry, we'll send you password recovery instructions
          </p>

          <form [formGroup]="forgotPasswordForm">
            <!-- Email Input -->
            <div class="form-group">
              <label for="forgot-email" class="form-label">
                <i class="ki-outline ki-phone"></i>
                Email or Mobile Number <span class="required"></span>
              </label>
              <input type="text" id="forgot-email" formControlName="input" class="form-control"
                [class.is-invalid]="isFieldInvalid('input', forgotPasswordForm)"
                placeholder="Enter your email or mobile number" required
                (blur)="markFieldAsTouched('input', forgotPasswordForm)" />
              <div *ngIf="isFieldInvalid('input', forgotPasswordForm)" class="invalid-feedback">
                {{ getFieldError("input", forgotPasswordForm) }}
              </div>
            </div>

            <!-- Error Message -->
            <div *ngIf="otpErrorMessage" class="alert alert-danger mt-3" role="alert">
              {{ otpErrorMessage }}
            </div>

            <!-- Send Verification Button -->
            <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingSendOtp"
              [disabled]="!isForgotPasswordFormValid() || isLoadingSendOtp" (click)="handleNextStepAndSendCode()">
              <span *ngIf="isLoadingSendOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
              {{ isLoadingSendOtp ? "Sending..." : "Send Verification Code" }}
            </button>
          </form>

          <!-- Back to Login -->
          <div class="help-text">
            <a (click)="backToLogin()" class="back-link" style="cursor: pointer">
              <i class="ki-outline ki-arrow-left"></i>
              Back to Login
            </a>
          </div>

          <!-- Need Help -->
          <div class="help-text">
            Need help? <a href="#" class="contact-link">Contact us</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 3: Verification Code -->
    <div *ngIf="currentStep === 3" class="registration-stepper-step">
      <div class="login-stepper">
        <div class="step-content">
          <h1 class="step-title">Enter Verification Code</h1>

          <!-- Verification Code Input -->
          <div class="verification-code-section" [formGroup]="forgotPasswordForm">
            <div formArrayName="verificationCode" class="verification-inputs">
              <div class="code-input" *ngFor="let ctrl of verificationCodeControls; let i = index">
                <input type="text" maxlength="1" class="verification-input" [formControlName]="i"
                  [class.is-invalid]="ctrl.invalid && ctrl.touched" (input)="autoFocusNext($event, i)" />
              </div>
            </div>
          </div>

          <!-- Countdown Timer -->
          <div class="countdown-section">
            <span class="countdown-text" *ngIf="!showResendButton">
              Resend in
              <span class="countdown-timer">
                0:{{ countdown < 10 ? "0" + countdown : countdown }} </span>
              </span>

              <button *ngIf="showResendButton" class="btn btn-link" (click)="onResendCode()">
                Resend Code
              </button>
          </div>

          <!-- OTP Error Message -->
          <div *ngIf="otpErrorMessage" class="alert alert-danger mt-3" role="alert">
            {{ otpErrorMessage }}
          </div>

          <!-- Next Button -->
          <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingCheckOtp"
            [disabled]="!isVerificationCodeValid() || isLoadingCheckOtp" (click)="checkOTP()">
            <span *ngIf="isLoadingCheckOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
            {{ isLoadingCheckOtp ? "Verifying..." : "Verified - Next" }}
          </button>

          <!-- Back to Forgot Password -->
          <div class="help-text">
            <a (click)="backToForgotPassword()" class="back-link" style="cursor: pointer">
              <i class="ki-outline ki-arrow-left"></i>
              Back to Forgot Password
            </a>
          </div>

          <!-- Help Text -->
          <div class="help-text">
            Need help? <span class="contact-link">Contact us</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 4: Reset Password Form -->
    <div *ngIf="currentStep === 4" class="registration-stepper-step">
      <div class="login-stepper">
        <div class="step-content">
          <h1 class="step-title">Reset Password</h1>
          <p class="step-subtitle">Enter your new password for your account</p>

          <form [formGroup]="resetPasswordForm">
            <!-- New Password -->
            <div class="form-group">
              <label for="new-password" class="form-label">
                <i class="ki-outline ki-lock"></i>
                New Password <span class="required"></span>
              </label>
              <input type="password" id="new-password" formControlName="newPassword" class="form-control"
                [class.is-invalid]="isFieldInvalid('newPassword', resetPasswordForm)"
                placeholder="Enter your new password" minlength="8" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$"
                title="Password must be at least 8 characters with uppercase, lowercase and number" required
                autocomplete="new-password" (blur)="markFieldAsTouched('newPassword', resetPasswordForm)" />
              <div *ngIf="isFieldInvalid('newPassword', resetPasswordForm)" class="invalid-feedback">
                {{ getFieldError("newPassword", resetPasswordForm) }}
              </div>
            </div>

            <!-- Confirm Password -->
            <div class="form-group">
              <label for="confirm-password" class="form-label">
                <i class="ki-outline ki-lock"></i>
                Confirm Password <span class="required"></span>
              </label>
              <input type="password" id="confirm-password" formControlName="password_confirmation" class="form-control"
                [class.is-invalid]="isFieldInvalid('password_confirmation', resetPasswordForm) || getFormError(resetPasswordForm)"
                placeholder="Re-enter your password" required autocomplete="new-password"
                (blur)="markFieldAsTouched('password_confirmation', resetPasswordForm)" />
              <div *ngIf="isFieldInvalid('password_confirmation', resetPasswordForm)" class="invalid-feedback">
                {{ getFieldError("password_confirmation", resetPasswordForm) }}
              </div>
              <div *ngIf="getFormError(resetPasswordForm)" class="invalid-feedback">
                {{ getFormError(resetPasswordForm) }}
              </div>
            </div>

            <!-- Reset Password Error Message -->
            <div *ngIf="resetPasswordErrorMessage" class="alert alert-danger mt-3" role="alert">
              {{ resetPasswordErrorMessage }}
            </div>

            <!-- Submit Button -->
            <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingResetPassword"
              [disabled]="!isResetPasswordFormValid() || isLoadingResetPassword" (click)="submitResetPassword()">
              <span *ngIf="isLoadingResetPassword" class="spinner-border spinner-border-sm me-2" role="status"></span>
              {{ isLoadingResetPassword ? "Saving..." : "Save New Password" }}
            </button>
          </form>

          <!-- Back to Verification Code -->
          <div class="help-text">
            <a (click)="backToVerificationCode()" class="back-link" style="cursor: pointer">
              <i class="ki-outline ki-arrow-left"></i>
              Back to Verification Code
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>