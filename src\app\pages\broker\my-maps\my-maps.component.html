<!-- Main Card -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h3 class="card-title fw-bold">My Maps</h3>
    <button
      class="btn btn-primary btn-sm"
      (click)="openUploadModal(uploadModal)"
    >
      <i class="bi bi-upload me-1"></i> Upload Map
    </button>
  </div>

  <div class="card-body">
    <!-- Files Grid -->
    <div class="row g-4">
      <!-- File Cards -->
      <div class="col-md-3 col-sm-6" *ngFor="let file of files">
        <div class="card h-100 shadow-sm">
          <div class="card-body p-3 text-center">
            <!-- Image Preview -->
            <img
              [src]="file.fileUrl"
              class="img-fluid rounded mb-2 cursor-pointer"
              (click)="openImageGallery(imageGalleryModal, files.indexOf(file))"
            />

            <!-- Description & Date -->
            <p class="fw-bold mb-1 mt-2">{{ file.description }}</p>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="col-12 text-center py-5" *ngIf="files.length === 0">
        <img
          src="./assets/media/svg/files/upload.svg"
          alt="Upload"
          class="w-100px mb-3"
        />
        <h5 class="text-gray-600">No Maps Uploaded</h5>
        <p class="text-muted">Upload your maps to get started.</p>
      </div>
    </div>
  </div>
</div>

<!-- Upload Modal -->
<ng-template #uploadModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">upload</h4>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>

  <div class="modal-body">
    <!-- File Input -->
    <div class="mb-3">
      <label for="fileUpload" class="form-label"> choose file </label>
      <input
        type="file"
        class="form-control"
        id="fileUpload"
        (change)="onFileSelected($event)"
      />
    </div>

    <!-- Description -->
    <div class="mb-3">
      <label for="fileDescription" class="form-label">description</label>
      <textarea
        class="form-control"
        id="fileDescription"
        rows="3"
        [(ngModel)]="fileDescription"
        placeholder="Enter a description for the file"
      ></textarea>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">
      cancel
    </button>
    <button
      type="button"
      class="btn btn-primary"
      [disabled]="!selectedFile || !fileDescription"
      (click)="uploadFile()"
    >
      upload
    </button>
  </div>
</ng-template>

<!-- Image Gallery Modal -->
<ng-template #imageGalleryModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">image gallery</h4>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>

  <div class="modal-body">
    <div class="text-center position-relative">
      <!-- Navigation -->
      <button
        *ngIf="files.length > 1"
        class="btn btn-icon btn-light-primary position-absolute top-50 start-0 translate-middle-y rounded-circle"
        (click)="prevImage()"
      >
        <i class="bi bi-chevron-left fs-2"></i>
      </button>

      <!-- Image -->
      <img
        *ngIf="files.length > 0"
        [src]="files[selectedImageIndex].fileUrl"
        class="img-fluid rounded gallery-image"
      />

      <button
        *ngIf="files.length > 1"
        class="btn btn-icon btn-light-primary position-absolute top-50 end-0 translate-middle-y rounded-circle"
        (click)="nextImage()"
      >
        <i class="bi bi-chevron-right fs-2"></i>
      </button>

      <!-- Description -->
      <div class="mt-4" *ngIf="files.length > 0">
        <h5 class="text-center">
          {{ files[selectedImageIndex].description }}
        </h5>
        <p class="text-muted text-center">
          {{ selectedImageIndex + 1 }} / {{ files.length }}
        </p>
      </div>

      <!-- Empty State -->
      <div *ngIf="files.length === 0" class="alert alert-info">
        no images available
      </div>
    </div>
  </div>
</ng-template>
