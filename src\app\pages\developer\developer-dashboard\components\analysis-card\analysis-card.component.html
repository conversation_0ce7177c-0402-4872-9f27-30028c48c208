<div class="card card-flush mb-5 bg-{{ backgroundColor }}">
  <div class="card-header border-0 py-8">
    <div class="row g-0">
      <h3 class="card-title d-flex flex-wrap align-items-center">
        <span class="badge text-wrap text-break badge-light-{{ backgroundColor }} fw-bold fs-6 fs-md-5 fs-lg-4"
              [ngClass]="{'text-white': backgroundColor === 'light-dark-blue', 'badge-dark-blue' : backgroundColor === 'light-dark-blue'}">
          {{ title }}
        </span>
      </h3>
    </div>
  </div>

  <div class="card-body p-0 d-flex flex-column">
    <div class="card-p pt-5 bg-body flex-grow-1 bg-{{ backgroundColor }}">
      <div class="row g-0">
        <div class="col">
          <div class="fs-7 fw-bold" [ngClass]="{'text-dark-blue': backgroundColor === 'light-dark-blue', 'text-white' : backgroundColor !== 'light-dark-blue'}">
            Total Number
          </div>
          <div class="d-flex align-items-center">
            <div class="fs-4 fw-bolder" [ngClass]="{'text-dark-blue': backgroundColor === 'light-dark-blue', 'text-white' : backgroundColor !== 'light-dark-blue'}">
              {{ totalRequests }}
            </div>
          </div>
        </div>

        <div class="col">
          <div class="fs-7 fw-bold" [ngClass]="{'text-dark-blue': backgroundColor === 'light-dark-blue', 'text-white' : backgroundColor !== 'light-dark-blue'}">
            Active
          </div>
          <div class="fs-4 fw-bolder" [ngClass]="{'text-dark-blue': backgroundColor === 'light-dark-blue', 'text-white' : backgroundColor !== 'light-dark-blue'}">
            {{ activeRequests }}
            <sub>+{{ getPercentage() }}%</sub>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
