import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { getCSSVariableValue } from '../../../../../_metronic/kt/_utils';

@Component({
  selector: 'app-contract-requests-chart',
  templateUrl: './contract-requests-chart.component.html',
  styleUrl: './contract-requests-chart.component.scss'
})
export class ContractRequestsChartComponent {

  @Input() cssClass: string = '';
  @Input() chartSize: number = 70;
  @Input() chartLine: number = 11;
  @Input() chartRotate?: number = 145;
  @Input() pending: number = 0;
  @Input() accepted: number = 0;
  @Input() declined: number = 0;

  contractStats: any = {};

  constructor() {}

  ngOnInit(): void {
    setTimeout(() => {
      this.initChart();
    }, 10);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      ('pending' in changes || 'accepted' in changes || 'declined' in changes) &&
      (this.pending + this.accepted + this.declined > 0)
    ) {
      setTimeout(() => this.initChart(), 10);
    }
  }

  private initChart() {
    const el = document.getElementById('kt_card_widget_17_chart_cus');
    if (!el) return;

    const options = {
      size: this.chartSize,
      lineWidth: this.chartLine,
      rotate: this.chartRotate ?? 145
    };

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = canvas.height = options.size;

    el.innerHTML = '';
    el.appendChild(canvas);

    if (!ctx) return;

    ctx.translate(options.size / 2, options.size / 2);
    ctx.rotate((-1 / 2 + options.rotate / 180) * Math.PI);

    const radius = (options.size - options.lineWidth) / 2;

    const total = this.pending + this.accepted + this.declined;
    if (total === 0) return;

    const pendingPercent = (this.pending / total) * 100;
    const acceptedPercent = (this.accepted / total) * 100;
    const declinedPercent = (this.declined / total) * 100;

    const drawCircle = (color: string, lineWidth: number, startPercent: number, endPercent: number) => {
      ctx.beginPath();
      ctx.arc(
        0,
        0,
        radius,
        (Math.PI * 2 * startPercent) / 100,
        (Math.PI * 2 * endPercent) / 100,
        false
      );
      ctx.strokeStyle = color;
      ctx.lineCap = 'round';
      ctx.lineWidth = lineWidth;
      ctx.stroke();
    };

    let startPercent = 0;

    drawCircle(getCSSVariableValue('--bs-warning'), options.lineWidth, startPercent, startPercent + pendingPercent);
    startPercent += pendingPercent;

    drawCircle(getCSSVariableValue('--bs-success'), options.lineWidth, startPercent, startPercent + acceptedPercent);
    startPercent += acceptedPercent;

    drawCircle(getCSSVariableValue('--bs-danger'), options.lineWidth, startPercent, startPercent + declinedPercent);
  }

}
