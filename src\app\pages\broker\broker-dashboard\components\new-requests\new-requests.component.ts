import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { BrokerService } from '../../../services/broker.service';

@Component({
  selector: 'app-new-requests',
  templateUrl: './new-requests.component.html',
  styleUrl: './new-requests.component.scss',
})
export class NewRequestsComponent {

  userId :any ;
  latestRequests: any[] = [];
  latestRequestsCount: number = 0;
  loading = false;
  errorMessage = '';
  orderBy: string;
  orderDir: string;

  constructor(protected cd: ChangeDetectorRef, private brokerService: BrokerService) {
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit(): void {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.userId = user?.id;
    this.fetchLatestRequests();
  }

  fetchLatestRequests(): void {
    this.loading = true;
    this.brokerService.getLatestRequests(this.userId).subscribe({
      next: (requests :any) => {
        this.latestRequests = requests.data;
        this.latestRequestsCount = requests.count;
        this.cd.detectChanges();
        console.log(this.latestRequests);
        console.log(this.latestRequestsCount);
        this.loading = false;
      },
      error: (error) => {
        this.errorMessage = 'Failed to load requests';
        console.error(error);
        this.loading = false;
      }
    });
  }

}
