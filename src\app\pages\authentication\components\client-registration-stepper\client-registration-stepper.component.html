<div class="client-registration-stepper">
  <!-- Stepper Header -->
  <div class="stepper-header">
    <h2 class="stepper-title">Register as a Client</h2>
    <div class="stepper-progress">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="(currentStep / totalSteps) * 100"></div>
      </div>
      <div class="d-flex">
        <span class="progress-text">Step {{ currentStep }} of {{ totalSteps }}</span>
        <button *ngIf="currentStep > 0 && currentStep < 4" type="button" class="back-to-previous"
          (click)="previousStep()">
          Back to previous step
        </button>
      </div>
    </div>
  </div>

  <!-- Stepper Content -->
  <form [formGroup]="registrationForm" class="stepper-form">
    <!-- Step 1: Gender Selection, Full Name, Email/Phone -->
    <div *ngIf="currentStep === 1" class="step-content">
      <h3 class="step-title">Choose Gender <span class="required"></span></h3>

      <!-- Gender Selection -->
      <div class="gender-selection">
        <div class="gender-buttons">
          <button type="button" class="gender-btn female" [class.selected]="selectedGender === 'female'"
            (click)="selectGender('female')">
            <i class="ki-outline ki-lovely"></i>
            Female
          </button>
          <button type="button" class="gender-btn" [class.selected]="selectedGender === 'male'"
            (click)="selectGender('male')">
            <i class="ki-outline ki-profile-user"></i>
            Male
          </button>
        </div>
        <div *ngIf="isFieldInvalid('gender')" class="invalid-feedback">
          Please select your gender
        </div>
      </div>

      <!-- Full Name -->
      <div class="form-group">
        <label for="fullName" class="form-label">
          <i class="ki-outline ki-user"></i>
          Full Name <span class="required"></span>
        </label>
        <input type="text" id="fullName" formControlName="fullName" class="form-control"
          [class.is-invalid]="isFieldInvalid('fullName')" placeholder="Name" pattern="[^0-9]*"
          title="Name cannot contain numbers" (blur)="markFieldAsTouched('fullName')" required />
        <div *ngIf="isFieldInvalid('fullName')" class="invalid-feedback">
          {{ getFieldError("fullName") }}
        </div>
      </div>

      <!-- Email or Phone -->
      <div class="form-group">
        <label class="form-label">
          <i class="ki-outline ki-phone"></i>
          Enter your email or phone number <span class="required"></span>
        </label>
        <input type="text" formControlName="email_phone" class="form-control"
          [class.is-invalid]="isFieldInvalid('email_phone')" placeholder="<EMAIL> or 01xxxxxxxxx"
          title="Enter a valid email address or phone number" autocomplete="email tel"
          (blur)="markFieldAsTouched('email_phone')" required />
        <div *ngIf="isFieldInvalid('email_phone')" class="invalid-feedback">
          {{ getFieldError("email_phone") }}
        </div>
      </div>

      <!-- Send Verification Button -->
      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingSendOtp"
        [disabled]="!isStep1Valid() || isLoadingSendOtp" (click)="handleNextStepAndSendCode()">
        <span *ngIf="isLoadingSendOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
        <span *ngIf="isLoadingSendOtp">Sending...</span>
        <span *ngIf="!isLoadingSendOtp">Send Verification Code</span>
      </button>

      <!-- Help Text -->
      <div class="help-text">
        Need help? <span class="contact-link">Contact us</span>
      </div>
    </div>

    <!-- Step 2: Verification Code -->
    <div *ngIf="currentStep === 2" class="step-content">
      <h3 class="step-title">Enter Verification Code</h3>

      <div class="verification-code-section">
        <div formArrayName="verificationCode" class="verification-inputs">
          <div class="code-input" *ngFor="let ctrl of verificationCodeControls; let i = index">
            <input type="text" maxlength="1" class="verification-input" [formControlName]="i"
              (input)="autoFocusNext($event, i)" />
          </div>
        </div>
      </div>

      <div class="countdown-section">
        <span class="countdown-text" *ngIf="!showResendButton">
          Resend in
          <span class="countdown-timer">
            0:{{ countdown < 10 ? "0" + countdown : countdown }} </span>
          </span>

          <button *ngIf="showResendButton" class="btn btn-link" (click)="onResendCode()">
            Resend Code
          </button>
      </div>

      <!-- OTP Error Message -->
      <div *ngIf="otpErrorMessage" class="alert alert-danger mt-3" role="alert">
        {{ otpErrorMessage }}
      </div>

      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingCheckOtp"
        [disabled]="!isStep2Valid() || isLoadingCheckOtp" (click)="checkOTP()">
        <span *ngIf="isLoadingCheckOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
        <span *ngIf="isLoadingCheckOtp">Verifying...</span>
        <span *ngIf="!isLoadingCheckOtp">Verification Code - Next</span>
      </button>

      <div class="help-text">
        Need help? <span class="contact-link">Contact us</span>
      </div>
    </div>

    <!-- Step 3: Username and Password -->
    <div *ngIf="currentStep === 3" class="step-content">
      <h3 class="step-title">Email, Phone and Password</h3>

      <!-- Email -->
      <div class="form-group">
        <label for="email" class="form-label">
          <i class="ki-outline ki-user"></i>
          Email
        </label>
        <input type="email" id="email" formControlName="email" class="form-control"
          [class.is-invalid]="isFieldInvalid('email')" placeholder="<EMAIL>.." autocomplete="email" />
        <div *ngIf="isFieldInvalid('email')" class="invalid-feedback">
          {{ getFieldError("email") }}
        </div>
      </div>

      <!-- Phone -->
      <div class="form-group">
        <label for="phone" class="form-label">
          <i class="ki-outline ki-phone"></i>
          Phone <span class="required"></span>
        </label>
        <input type="tel" id="phone" formControlName="phone" class="form-control"
          [class.is-invalid]="isFieldInvalid('phone')" placeholder="01xxxxxxxxx" required autocomplete="tel" />
        <div *ngIf="isFieldInvalid('phone')" class="invalid-feedback">
          {{ getFieldError("phone") }}
        </div>
      </div>

      <!-- Password -->
      <div class="form-group">
        <label for="password" class="form-label">
          <i class="ki-outline ki-lock"></i>
          Password <span class="required"></span>
        </label>
        <input type="password" id="password" formControlName="password" class="form-control"
          [class.is-invalid]="isFieldInvalid('password')" placeholder="********" minlength="8"
          pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$"
          title="Password must be at least 8 characters with uppercase, lowercase and number" required
          autocomplete="new-password" />
        <div *ngIf="isFieldInvalid('password')" class="invalid-feedback">
          {{ getFieldError("password") }}
        </div>
      </div>

      <!--Confirm Password -->
      <div class="form-group">
        <label for="confirmPassword" class="form-label">
          <i class="ki-outline ki-lock"></i>
          Confirm Password <span class="required"></span>
        </label>
        <input type="password" id="confirmPassword" formControlName="password_confirmation" class="form-control"
          [class.is-invalid]="
            isFieldInvalid('password_confirmation') || getFormError()
          " placeholder="********" required autocomplete="new-password" />
        <div *ngIf="isFieldInvalid('password_confirmation')" class="invalid-feedback">
          {{ getFieldError("password_confirmation") }}
        </div>
        <div *ngIf="getFormError()" class="invalid-feedback">
          {{ getFormError() }}
        </div>
      </div>

      <!-- Terms Agreement -->
      <div class="form-group">
        <div class="form-check">
          <input type="checkbox" id="agreeTerms" formControlName="agreeTerms" class="form-check-input"
            [class.is-invalid]="isFieldInvalid('agreeTerms')" />
          <label for="agreeTerms" class="form-check-label">
            I agree to the Terms and Conditions <span class="required"></span>
          </label>
        </div>
        <div *ngIf="isFieldInvalid('agreeTerms')" class="invalid-feedback">
          {{ getFieldError("agreeTerms") }}
        </div>
      </div>

      <!-- Create Account Button -->
      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingCreateAccount"
        [disabled]="!isStep3Valid() || isLoadingCreateAccount" (click)="createAccount()">
        <span *ngIf="isLoadingCreateAccount" class="spinner-border spinner-border-sm me-2" role="status"></span>
        <span *ngIf="isLoadingCreateAccount">Creating Account...</span>
        <span *ngIf="!isLoadingCreateAccount">Create Account</span>
      </button>
    </div>

    <!-- Step 4: Success Page -->
    <div *ngIf="currentStep === 4" class="step-content success-step">
      <div class="success-content">
        <div class="success-icon">
          <i class="ki-outline ki-check-circle"></i>
        </div>

        <h3 class="success-title">registration Success</h3>

        <p class="success-message">
          Your account has been successfully created. You can now enjoy the
          various and amazing services provided by Easy deal through the website
          or dashboard.
        </p>

        <div class="success-illustration">
          <!-- Success illustration will go here -->
          <img src="/assets/media/login/successfully.png" alt="Success" class="success-image" />
        </div>

        <button type="button" class="btn btn-primary btn-success-action" [routerLink]="['/requests']">
          Go to website
        </button>

        <div class="additional-info">
          <span class="info-link">Learn all about your account and how to get started</span>
        </div>
      </div>
    </div>
  </form>
</div>