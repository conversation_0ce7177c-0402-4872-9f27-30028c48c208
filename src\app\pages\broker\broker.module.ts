import { NgModule } from '@angular/core';
import { NgApexchartsModule } from 'ng-apexcharts';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { BrokerDashboardComponent } from './broker-dashboard/broker-dashboard.component';
import { DataandpropertiesComponent } from './dataandproperties/dataandproperties.component';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';
import { PieChartComponent } from './broker-dashboard/components/pie-chart/pie-chart.component';
import { DashboardCardComponent } from './broker-dashboard/components/dashboard-card/dashboard-card.component';
import { AnalysisCardComponent } from './broker-dashboard/components/analysis-card/analysis-card.component';
import { NewRequestsComponent } from './broker-dashboard/components/new-requests/new-requests.component';
import { NewRequestCardComponent } from './broker-dashboard/components/new-request-card/new-request-card.component';
import { SpecializationsFilterComponent } from './broker-dashboard/components/specializations-filter/specializations-filter.component';
import { DropdownMenusModule } from '../../_metronic/partials/content/dropdown-menus/dropdown-menus.module';
import { BrokerTitleComponent } from './shared/broker-title/broker-title.component';
import { PropertiestableComponent } from './dataandproperties/components/propertiestable/propertiestable.component';
import { EmptyPropertiesCardComponent } from './dataandproperties/components/empty-properties-card/empty-properties-card.component';
import { SuccessAddingPropertyCardComponent } from './dataandproperties/components/success-adding-property-card/success-adding-property-card.component';
import { PublishPropertyCardComponent } from './dataandproperties/components/publish-property-card/publish-property-card.component';
import { SubscriptionComponent } from './subscription/subscription.component';
import { ViewApartmentModelComponent } from '../shared/view-apartment-model/view-apartment-model.component';
import { MyAddsComponent } from './my-adds/my-adds.component';
import { MyMapsComponent } from './my-maps/my-maps.component';
import { StepperModalComponent } from './shared/stepper-modal/stepper-modal.component';
import { PaginationComponent } from 'src/app/pagination/pagination.component';
import { AddPropertyComponent } from './add-property/add-property.component';
import { PropertyDetailsComponent } from './property-details/property-details.component';
import { UnitFilterComponent } from './dataandproperties/components/unit-filter/unit-filter.component';

@NgModule({
  declarations: [
    PieChartComponent,
    StepperModalComponent,
    DashboardCardComponent,
    AnalysisCardComponent,
    NewRequestsComponent,
    NewRequestCardComponent,
    BrokerDashboardComponent,
    DataandpropertiesComponent,
    SpecializationsFilterComponent,
    BrokerTitleComponent,
    PropertiestableComponent,
    EmptyPropertiesCardComponent,
    SuccessAddingPropertyCardComponent,
    PublishPropertyCardComponent,
    SubscriptionComponent,
    AddPropertyComponent,
    UnitFilterComponent,
  ],
  imports: [
    RouterModule.forChild([
      { path: 'dashboard', component: BrokerDashboardComponent },
      { path: 'dataandproperties', component: DataandpropertiesComponent },
      { path: 'add-property', component: AddPropertyComponent },
      { path: 'developers', redirectTo: '/developer', pathMatch: 'full' },
      { path: 'subscriptions', component: SubscriptionComponent },
      { path: 'Adds', component: MyAddsComponent },
      { path: 'property-details/:id', component: PropertyDetailsComponent },
      { path: 'Maps', component: MyMapsComponent },
      { path: 'stepper-modal', component: StepperModalComponent },
    ]),
    NgApexchartsModule,
    SharedModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgbModule,
    DropdownMenusModule,
    ViewApartmentModelComponent,
    PaginationComponent,
    PropertyDetailsComponent,
  ],
  exports: [
    BrokerTitleComponent,
    StepperModalComponent,
    EmptyPropertiesCardComponent,
    SuccessAddingPropertyCardComponent,
    PublishPropertyCardComponent,
    UnitFilterComponent,
  ],
})
export class BrokerModule {}
