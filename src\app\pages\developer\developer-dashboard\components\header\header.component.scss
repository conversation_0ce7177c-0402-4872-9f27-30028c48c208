.header {
  padding: 15px 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;

  flex-direction: column;
  gap: 15px;
}

.header-center {
  text-align: center;
}

.header-right {
  display: flex;
  gap: 15px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #2c3e50;
}

.subtitle {
  margin: 5px 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  color: #7f8c8d;
  font-size: 14px;
  padding: 5px 10px;
  border-radius: 4px;

  i {
    font-size: 16px;
    padding: 8px;
    border-radius: 50%;
    background-color: #ffffff;
  }

  &:hover {
    background-color: #121212;
  }
}

@media (max-width: 768px) {
  .header-right {
    display: none;
  }

  .header-center {
    text-align: right;
  }
}
