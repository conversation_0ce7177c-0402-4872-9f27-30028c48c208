import { ChangeDetectorRef, Component } from '@angular/core';
import { BaseGridComponent } from '../shared/base-grid/base-grid.component';
import { DevelopersService } from '../broker/services/developers.service';
import { ContractService } from './services/contract.service';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import Swal from 'sweetalert2';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormGroup } from '@angular/forms';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-developers',
  templateUrl: './developers.component.html',
  styleUrl: './developers.component.scss',
})
export class DevelopersComponent extends BaseGridComponent {
  activeTab: 'all' | 'contracted' | 'not-contracted' = 'all';
  selectedDeveloper: any = null;
  searchInput: string = '';
  private searchSubject = new Subject<string>();
  brokerId: number ;

  stepForm: FormGroup;

  constructor(
    protected cd: ChangeDetectorRef,
    private developersService: DevelopersService,
    private contractService: ContractService,
    private modalService: NgbModal,
    private fb: FormBuilder
  ) {
    super(cd);
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.brokerId = user?.brokerId;
    this.setService(developersService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
    this.initForms();
  }

  initForms() {
    this.stepForm = this.fb.group({
      image: [[]],
      idFront: [[]],
      idBack: [[]],
    });
  }

  onFileChange(event: any, fileType: string) {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);

      this.stepForm.patchValue({
        [fileType]: fileArray,
      });
    }
  }

  getFileCount(fieldName: string): number {
    const files = this.stepForm.get(fieldName)?.value;
    return files && Array.isArray(files) ? files.length : 0;
  }

  openContractModal(content: any, developer?: any) {
    if (developer) {
      this.selectedDeveloper = developer;
      console.log('Developer ID:', developer.developerId);
    }

    this.modalService.open(content, {
      centered: true,
      size: 'md',
    });
  }

  sendContractRequest() {
    const httpFormData = new FormData();

    const developerId = String(this.selectedDeveloper.developerId);
    httpFormData.append('developerId', developerId);
    httpFormData.append('brokerId', String(this.brokerId));

    const fileFields = ['image', 'idFront', 'idBack'];

    fileFields.forEach((field) => {
      const files = this.stepForm.get(field)?.value;
      if (files && files.length) {
        httpFormData.append(field, files[0]);
      }
    });

    this.contractService.createContractRequest(httpFormData).subscribe({
      next: async (response) => {
        await Swal.fire('Contract request submitted:', '', response.status);
        this.stepForm.reset({
          image: [],
          idFront: [],
          idBack: [],
        });
        this.modalService.dismissAll();
        this.reloadTable(this.page);
      },
      error: (err) => {
        console.error('Error sending contract request:', err);
        Swal.fire(err.message, '', err.status);
      },
    });
  }

  getDeveloperStatus(brokers: any[]): string {
    const isContracted = brokers.some(
      (broker) => broker.brokerId === this.brokerId
    );
    return isContracted ? 'contracted' : 'send contract request';
  }

  setActiveTab(tab: 'all' | 'contracted' | 'not-contracted') {
    console.log(tab);
    this.activeTab = tab;
    this.reloadTable(this.page);
  }

  private setupSearchDebounce() {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.searchInput = searchTerm;
      this.page.pageNumber = 0;
      this.reloadTable(this.page);
    });
  }

  onSearch(event: any) {
    this.searchInput = event.target.value;
    this.page.pageNumber = 0;
    this.page.filters = { search:this.searchInput};
    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    const params = {
      pageNumber: this.page.pageNumber,
      size: this.page.size,
      orderBy: this.orderBy,
      orderDir: this.orderDir,
    };

    this.page.size = environment.TABLE_LIMIT;
    this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
    this.loading = true;
    console.log('Request params:', params);

    await this._service.getAll(this.page).subscribe(
      (response: any) => {
        console.log('Response data:', response.data);
        let data = response.data;
        this.rows = Array.isArray(response.data) ? response.data : [];
        this.rows = [...this.rows];

        if (this.activeTab === 'contracted') {
          this.rows = data.filter(
            (row: any) => this.getDeveloperStatus(row.brokers) === 'contracted'
          );
        } else if (this.activeTab === 'not-contracted') {
          this.rows = data.filter(
            (row: any) => this.getDeveloperStatus(row.brokers) === 'send contract request'
          );
        }

        this.page.totalElements = response.count;
        this.page.count = Math.ceil(response.count / this.page.size);

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
        MenuComponent.reinitialization();
      },
      (error: any) => {
        console.error('Error loading data:', error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire('Failed to load data. Please try again later.', '', 'error');
      }
    );
  }
}
