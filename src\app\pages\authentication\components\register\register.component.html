<div class="register-container">
  <!-- Welcome Header -->
  <!-- <div class="welcome-header">
    <h1 class="welcome-title">مرحباً بك في إيزي ديل</h1>
  </div> -->

  <!-- Registration Form Card -->
  <div class="register-card">
    <!-- Step 1: User Type Selection -->
    <div *ngIf="currentStep === 1" class="user-type-selection">
      <h2 class="form-title">Choose your account type</h2>
      <p class="form-subtitle">
        Choose the account type that you want to register with, which matches
        your work and needs in Easy deal
      </p>

      <!-- User Type Options -->
      <div class="user-type-options">
        <div
          class="user-type-option"
          [class.selected]="selectedUserType === 'client'"
          (click)="selectUserType('client')"
        >
          <span class="option-label">Client</span>
          <div class="option-icon">
            <i class="ki-outline ki-user"></i>
          </div>
        </div>

        <div
          class="user-type-option"
          [class.selected]="selectedUserType === 'broker'"
          (click)="selectUserType('broker')"
        >
          <span class="option-label">Broker</span>
          <div class="option-icon">
            <i class="ki-outline ki-people"></i>
          </div>
        </div>

        <div
          class="user-type-option"
          [class.selected]="selectedUserType === 'developer'"
          (click)="selectUserType('developer')"
        >
          <span class="option-label">Developer</span>
          <div class="option-icon">
            <i class="ki-outline ki-home"></i>
          </div>
        </div>
      </div>

      <!-- Continue Button -->
      <button
        class="continue-btn"
        [disabled]="!selectedUserType"
        (click)="nextStep()"
      >
        Choose
      </button>

      <!-- Login Link -->
      <div routerLink="../login" class="login-link-container cursor-pointer">
        <a class="login-link">
          <i class="ki-outline ki-arrow-right fs-3"></i>
        </a>
        <span class="login-text">Already have an account? Go to login</span>
      </div>

      <!-- Support Link -->
      <div class="support-link">
        <span class="support-text">Need help? Contact us</span>
      </div>
    </div>

    <!-- Step 2: Registration Stepper -->
    <div *ngIf="currentStep === 2" class="registration-stepper-step">
      <!-- Client Registration Stepper -->
      <app-client-registration-stepper
        *ngIf="selectedUserType === 'client'"
        (onBack)="previousStep()"
        (onComplete)="onRegistrationComplete($event)"
      >
      </app-client-registration-stepper>

      <!-- Broker Registration Stepper -->
      <app-broker-registration-stepper
        *ngIf="selectedUserType === 'broker'"
        (onBack)="previousStep()"
        (onComplete)="onRegistrationComplete($event)"
      >
      </app-broker-registration-stepper>

      <!-- Developer Registration Stepper -->
      <app-developer-registration-stepper
        *ngIf="selectedUserType === 'developer'"
        (onBack)="previousStep()"
        (onComplete)="onRegistrationComplete($event)"
      >
      </app-developer-registration-stepper>
    </div>
  </div>
</div>
