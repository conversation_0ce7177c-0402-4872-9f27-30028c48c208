export class AccountTypeMapper {

  static getAccountTypeBadge(type : string) : string{

    switch(type) {

      case 'Free':
        return 'badge-light-primary';

      case 'Silver Account':
        return 'badge-light-secondary';

      case 'Bronze Account':
        return 'badge-light-danger';

      case 'Golden Account':
        return 'badge-light-warning';

      default:
        return 'badge-light-primary';
    }
  }
}
