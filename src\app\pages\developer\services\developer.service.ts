import { Injectable } from '@angular/core';
import { AbstractCrudService } from '../../shared/services/abstract-crud.service';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class DevelopersService extends AbstractCrudService {

  apiUrl = `${environment.apiUrl}/developers`;

  getDeveloperStatistics(id: number): Observable<any> {
    const params = {
      limit: 10,
      offset: 0,
      sort: 'DESC',
      sortBy: 'id',
      developerId: id
    };

    return this.http.get(`${environment.apiUrl}/broker/request`, { params });
  }

  getDeveloperPieChartStatistics(id: number): Observable<any> {
    return this.http.get(`${environment.apiUrl}/developers/statistics/${id}`);
  }
}
