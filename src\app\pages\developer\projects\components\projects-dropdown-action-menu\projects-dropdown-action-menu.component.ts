import { Component, OnInit, HostBinding, Input } from '@angular/core';

@Component({
  selector: 'app-projects-dropdown-action-menu',
  templateUrl: './projects-dropdown-action-menu.component.html',
  styleUrl: './projects-dropdown-action-menu.component.scss',
})
export class ProjectsDropdownActionMenuComponent implements OnInit {
  @HostBinding('class') class =
    'menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px';
  @HostBinding('attr.data-kt-menu') dataKtMenu = 'true';

  @Input() id: number | any;

  constructor() {}

  ngOnInit(): void {}
}
