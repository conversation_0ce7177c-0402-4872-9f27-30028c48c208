// Required field indicator
.required {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

// Alert styles
.alert {
  padding: 12px 16px;
  margin-bottom: 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
  direction: ltr;

  &.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
  }
}

// Variables
$primary-color: #232176;
$success-color: #28a745;
$secondary-color: #34A853;
$blue-color: #4c63d2;
$text-color: #333;
$muted-color: #6c757d;
$border-color: #ddd;
$bg-light: #f8f9fa;

:host {
  display: block;
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-image: url('/angular/assets/media/login/EaseDealPage.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px 20px 20px 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  direction: ltr;
  text-align: left;
  position: relative;
}

.register-card {
  width: 100%;
  max-width: 450px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
  position: relative;
  z-index: 9999;
  margin-left: auto;
  margin-right: 100px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
  padding: 40px;
  direction: ltr;
  text-align: left;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// User Type Selection Styles
.user-type-selection {
  text-align: center;

  .form-title {
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
  }

  .form-subtitle {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 30px;
  }

  .user-type-options {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-bottom: 30px;

    .user-type-option {
      flex: 1;
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 15px;
      padding: 10px 15px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      &:hover {
        border-color: #667eea;
        background: #f0f2ff;
      }

      &.selected {
        border-color: #667eea;
        background: #2a44b6;
        color: white;

        .option-icon i {
          color: white;
        }
      }

      .option-icon {
        margin: 0;

        i {
          color: #2a44b6;
          transition: color 0.3s ease;
          font-size: 1.2rem;
        }
      }

      .option-label {
        font-weight: 500;
        font-size: 0.9rem;
        margin: 0;
      }
    }
  }

  .continue-btn {
    width: 100%;
    padding: 15px;
    background: #1e35a6;
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-bottom: 20px;

    &:hover:not(:disabled) {
      background: #667eea;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .login-link-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;

    .login-text {
      color: #333;
      font-size: 0.9rem;
    }

    .login-link {
      color: #667eea;
      text-decoration: none;

      i {
        font-size: 1rem;
      }

      &:hover {
        color: #5a6fd8;
      }
    }
  }

  .support-link {
    text-align: center;

    .support-text {
      color: #28a745;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Login Form Styles
.login-stepper {
  .back-to-selection {
    margin-bottom: 20px;

    .back-btn {
      background: none;
      border: none;
      color: #667eea;
      font-size: 0.9rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 5px 0;

      &:hover {
        color: #5a6fd8;
        text-decoration: underline;
      }

      i {
        font-size: 1rem;
      }
    }
  }

  .step-content {
    min-height: 50px;
    margin-bottom: 5px;
    text-align: center;
    padding: 2px 5px;
  }

  .step-title {
    color: $primary-color;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
    padding-bottom: 8px;

  }

  .login-form-content {
    text-align: center;

    .login-title {
      color: #333;
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .login-subtitle {
      color: #666;
      font-size: 0.9rem;
      margin-bottom: 25px;
    }

    .form-group {
      margin-bottom: 20px;
      text-align: left;

      label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
        font-size: 1rem;
        justify-content: flex-start;
        text-align: left;
        width: 100%;

        i {
          color: #28a745;
          font-size: 1rem;
        }
      }

      .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;

        &:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        &.is-invalid {
          border-color: #dc3545;
          box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }

        &::placeholder {
          color: #999;
        }
      }

      .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
        font-weight: 500;
        text-align: left;
        direction: ltr;
      }

      .form-check {
        display: flex;
        align-items: center;
        gap: 8px;
        text-align: left;

        .form-check-input {
          width: 18px;
          height: 18px;
          cursor: pointer;

          &.is-invalid {
            border-color: #dc3545;
          }
        }

        .form-check-label {
          font-size: 14px;
          color: #333;
          cursor: pointer;
        }
      }
    }

    .forgot-password {
      text-align: center;
      margin-top: 15px;

      .forgot-link {
        color: #667eea;
        text-decoration: none;
        font-size: 0.9rem;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// Registration Stepper Step Styles
.registration-stepper-step {
  width: 100%;
}

// Form Group Styles
.form-group {
  margin-bottom: 20px;
  text-align: left;
  width: 100%;

  .form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 1rem;
    justify-content: flex-start;
    text-align: left;
    width: 100%;

    i {
      color: #667eea;
      font-size: 1rem;
    }
  }
}

// Progress Bar Styles
.progress-bar-container {
  margin: 20px 0;

  .progress-bar {
    display: flex;
    gap: 5px;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;

    .progress-fill {
      flex: 1;
      background: $success-color;
      border-radius: 2px;
    }
  }
}

// Back to Login Styles
.back-to-login {
  text-align: center;
  margin-top: 15px;

  .back-btn {
    background: none;
    border: none;
    color: #667eea;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
    margin: 0 auto;

    &:hover {
      color: #5a6fd8;
      text-decoration: underline;
    }

    i {
      font-size: 1rem;
    }
  }

  // Buttons & Help
  .btn-verification {
    width: 424.39px;
    padding: 18px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 25px;
    margin: 30px auto 35px;
    background: #1e35a6;
    color: white;
    text-align: center;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover:not(:disabled) {
      background: #667eea;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.loading {
      position: relative;
      pointer-events: none;

      .spinner-border {
        width: 1rem;
        height: 1rem;
        border-width: 0.125em;
      }
    }

    .spinner-border {
      display: inline-block;
      width: 1rem;
      height: 1rem;
      vertical-align: text-bottom;
      border: 0.125em solid currentColor;
      border-right-color: transparent;
      border-radius: 50%;
      animation: spinner-border 0.75s linear infinite;
    }
  }

  @keyframes spinner-border {
    to {
      transform: rotate(360deg);
    }
  }

  .help-text {
    text-align: center;
    margin-top: 25px;
    margin-bottom: 20px;
    font-size: 13px;
    color: $muted-color;

    .contact-link {
      color: #28a745;
      cursor: pointer;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }

    .register-link {
      color: #007bff;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // Register Section Styling
  .register-section {
    margin-top: 20px;
    margin-bottom: 15px;

    .register-text {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      .register-link {
        color: #6c757d;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;

        .register-message {
          color: #6c757d;
          font-size: 13px;
          font-weight: 400;

          .create-link {
            color: #007bff;
            font-weight: 500;
          }
        }

        i {
          font-size: 1rem;
          color: #6c757d;
        }

        &:hover {
          .register-message {
            .create-link {
              text-decoration: underline;
            }
          }

          i {
            transform: translateX(2px);
            color: #007bff;
          }
        }
      }
    }
  }

  // Form Row (Forgot Password & Remember Me)
  .form-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;

    .forgot-password-link {
      text-align: left;

      .forgot-link {
        color: #28a745;
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .form-check {
      display: flex;
      align-items: center;
      gap: 8px;
      text-align: right;

      .form-check-input {
        width: 18px;
        height: 18px;
        cursor: pointer;
      }

      .form-check-label {
        font-size: 14px;
        color: #333;
        cursor: pointer;
      }
    }
  }
}

// Form Row (Forgot Password & Remember Me)
.form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;

  .forgot-password-link {
    text-align: left;

    .forgot-link {
      color: #28a745;
      text-decoration: none;
      font-size: 0.9rem;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .form-check {
    display: flex;
    align-items: center;
    gap: 8px;
    text-align: right;

    .form-check-input {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }

    .form-check-label {
      font-size: 14px;
      color: #333;
      cursor: pointer;
    }
  }
}

// Form Row (Forgot Password & Remember Me)
.form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;

  .forgot-password-link {
    text-align: left;

    .forgot-link {
      color: #28a745;
      text-decoration: none;
      font-size: 0.9rem;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .form-check {
    display: flex;
    align-items: center;
    gap: 8px;
    text-align: right;

    .form-check-input {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }

    .form-check-label {
      font-size: 14px;
      color: #333;
      cursor: pointer;
    }
  }
}

// Step-specific styles
.step-subtitle {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 20px;
  text-align: center;
  line-height: 1.5;
}

.back-link {
  color: #28a745;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  margin: 15px 0;

  &:hover {
    text-decoration: underline;
  }

  i {
    font-size: 1rem;
  }
}

.contact-link {
  color: #28a745;
  text-decoration: none;
  font-weight: 500;

  &:hover {
    text-decoration: underline;
  }
}

// Verification Code Styles
.verification-code-section {
  margin: 15px 0;
  text-align: center;

  .verification-inputs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 12px;

    .code-input {
      .verification-input {
        width: 50px;
        height: 50px;
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
        border: 2px solid #ddd;
        border-radius: 8px;
        background-color: #fff;
        color: #333;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: #28a745;
          box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        &.is-invalid {
          border-color: #dc3545;
          box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        &[type=number] {
          -moz-appearance: textfield;
        }
      }
    }
  }
}

.countdown-section {
  text-align: center;
  margin: 25px 0;

  .countdown-text {
    color: #6c757d;
    font-size: 0.9rem;

    .countdown-timer {
      color: #28a745;
      font-weight: bold;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .register-container {
    padding: 10px 10px 10px 0;
    justify-content: flex-start;
  }

  .register-card {
    max-width: 100%;
    border-radius: 10px;
    margin-left: auto;
    margin-right: auto;
    padding: 30px 20px;
  }

  .user-type-selection {
    .user-type-options {
      flex-direction: column;
      gap: 10px;

      .user-type-option {
        padding: 15px;
      }
    }
  }
}

@media (max-width: 480px) {
  .register-card {
    margin: 0 10px;
    max-width: calc(100% - 20px);
    padding: 20px 15px;
  }

  .user-type-selection {
    .form-title {
      font-size: 1.2rem;
    }

    .form-subtitle {
      font-size: 0.8rem;
    }
  }
}


