import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { getCSSVariableValue } from 'src/app/_metronic/kt/_utils';

@Component({
  selector: 'app-project-pie-chart',
  templateUrl: './project-pie-chart.component.html',
  styleUrls: ['./project-pie-chart.component.scss']
})
export class ProjectPieChartComponent implements OnInit, OnChanges {

  @Input() cssClass: string = '';
  @Input() chartSize: number = 70;
  @Input() chartLine: number = 11;
  @Input() chartRotate?: number = 145;
  @Input() newCount: number = 0;
  @Input() availableCount: number = 0;
  @Input() soldCount: number = 0;
  @Input() reservedCount: number = 0;

  contractStats: any = {};

  constructor() {}

  ngOnInit(): void {
    setTimeout(() => {
      this.initChart();
    }, 10);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      ('newCount' in changes || 'availableCount' in changes || 'soldCount' in changes || 'reservedCount' in changes) &&
      (this.newCount + this.availableCount + this.soldCount + this.reservedCount > 0)
    ) {
      setTimeout(() => this.initChart(), 10);
    }
  }

  private initChart() {
    const el = document.getElementById('kt_units_statistics');
    if (!el) return;

    const options = {
      size: this.chartSize,
      lineWidth: this.chartLine,
      rotate: this.chartRotate ?? 145
    };

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = canvas.height = options.size;

    el.innerHTML = '';
    el.appendChild(canvas);

    if (!ctx) return;

    ctx.translate(options.size / 2, options.size / 2);
    ctx.rotate((-1 / 2 + options.rotate / 180) * Math.PI);

    const radius = (options.size - options.lineWidth) / 2;

    const total = this.newCount + this.availableCount + this.soldCount + this.reservedCount;
    if (total === 0) return;

    const newPercentage = (this.newCount / total) * 100;
    const availablePercentage = (this.availableCount / total) * 100;
    const soldPercentage = (this.soldCount / total) * 100;
    const reservedPercentage = (this.reservedCount / total) * 100;

    const drawCircle = (color: string, lineWidth: number, startPercent: number, endPercent: number) => {
      ctx.beginPath();
      ctx.arc(
        0,
        0,
        radius,
        (Math.PI * 2 * startPercent) / 100,
        (Math.PI * 2 * endPercent) / 100,
        false
      );
      ctx.strokeStyle = color;
      ctx.lineCap = 'round';
      ctx.lineWidth = lineWidth;
      ctx.stroke();
    };

    let startPercent = 0;

    drawCircle(getCSSVariableValue('--bs-warning'), options.lineWidth, startPercent, startPercent + newPercentage);
    startPercent += newPercentage;

    drawCircle(getCSSVariableValue('--bs-success'), options.lineWidth, startPercent, startPercent + availablePercentage);
    startPercent += availablePercentage;

    drawCircle(getCSSVariableValue('--bs-primary'), options.lineWidth, startPercent, startPercent + soldPercentage);
    startPercent += soldPercentage;

    drawCircle(getCSSVariableValue('--bs-danger'), options.lineWidth, startPercent, startPercent + reservedPercentage);
  }

}
