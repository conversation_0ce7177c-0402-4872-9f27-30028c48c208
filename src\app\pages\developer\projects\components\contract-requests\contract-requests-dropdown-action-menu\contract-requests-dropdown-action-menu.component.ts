import {
  Component,
  OnInit,
  HostBinding,
  Input,
  AfterViewInit,
  Output,
  EventEmitter,
} from '@angular/core';
import { ContractService } from '../../../../services/contract.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-contract-requests-dropdown-action-menu',
  templateUrl: './contract-requests-dropdown-action-menu.component.html',
  styleUrl: './contract-requests-dropdown-action-menu.component.scss',
})
export class ContractRequestsDropdownActionMenuComponent {
  @HostBinding('class') class = 'dropdown-menu';

  @Input() id: number | any; // This will be the request ID
  @Output() actionCompleted = new EventEmitter<void>();

  constructor(private contractService: ContractService) {}

  handleRequestAction(action: 'accepted' | 'declined') {
    console.log('Contract Request ID received:', this.id);
    console.log('Type of ID:', typeof this.id);

    if (!this.id || this.id === undefined || this.id === null) {
      Swal.fire(
        'Error',
        'Contract request ID is missing. Please refresh the page and try again.',
        'error'
      );
      return;
    }

    const isApprove = action === 'accepted';

    Swal.fire({
      title: `${isApprove ? 'accepted' : 'declined'} Request`,
      text: `Are you sure you want to ${
        isApprove ? 'accepted' : 'declined'
      } this contract request?`,
      icon: isApprove ? 'question' : 'warning',
      showCancelButton: true,
      confirmButtonColor: isApprove ? '#28a745' : '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: `Yes, ${isApprove ? 'accepted' : 'declined'}`,
      cancelButtonText: 'Cancel',
    }).then((result) => {
      if (result.isConfirmed) {
        const requestObservable = isApprove
          ? this.contractService.approveRequest(this.id)
          : this.contractService.rejectRequest(this.id);

        requestObservable.subscribe({
          next: () => {
            Swal.fire(
              `${isApprove ? 'accepted' : 'declined'}!`,
              `Contract request has been ${
                isApprove ? 'accepted' : 'declined'
              }.`,
              'success'
            );
            this.actionCompleted.emit();
          },
          error: (error) => {
            console.error(`Error ${action}ing request:`, error);
            Swal.fire(
              'Error',
              `Failed to ${action} request. Please try again.`,
              'error'
            );
          },
        });
      }
    });
  }

  onApprove() {
    this.handleRequestAction('accepted');
  }

  onReject() {
    this.handleRequestAction('declined');
  }
}
