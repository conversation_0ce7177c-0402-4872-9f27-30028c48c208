import { Component, Input } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-dashboard-card',
  templateUrl: './dashboard-card.component.html',
  styleUrls: ['./dashboard-card.component.scss']
})
export class DashboardCardComponent {

  @Input() title: string = '';
  @Input() subTitle: string = '';
  @Input() icon: { type: 'fontawesome' | 'keenicon' | 'svg', name?: string, iconType?: string, svgContent?: string } = { type: 'fontawesome' };
  @Input() buttonTitle: string = '';
  @Input() buttonIcon: string = '';
  @Input() buttonLink: string = '';

  constructor(private sanitizer: DomSanitizer) {}

  // Sanitize SVG content to prevent XSS
  getSafeSvgContent(): SafeHtml {
    return this.icon.svgContent ? this.sanitizer.bypassSecurityTrustHtml(this.icon.svgContent) : '';
  }

  // Check if icon is Font Awesome
  isFontAwesomeIcon(): boolean {
    return this.icon.type === 'fontawesome' && !!this.icon.name;
  }

  // Check if icon is Keen
  isKeenIcon(): boolean {
    return this.icon.type === 'keenicon' && !!this.icon.name && !!this.icon.iconType;
  }

  // Check if icon is custom SVG
  isSvgIcon(): boolean {
    return this.icon.type === 'svg' && !!this.icon.svgContent;
  }

  // Provide fallback values for KeenIcon properties
  getKeenIconName(): string {
    return this.icon.name || 'default-icon'; // Fallback icon name
  }

  getKeenIconType(): string {
    return this.icon.iconType || 'outline'; // Fallback icon type
  }
}
