<div class="mb-5 mt-0">
  <app-broker-title *ngIf="user?.role === 'broker'"></app-broker-title>
  <app-developer-header
    *ngIf="user?.role === 'developer'"
    [title]="'unit'"
    [subtitle]="'To view existing unite'"
  >
  </app-developer-header>
</div>
<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1 mt-3">Units</h1>
          </div>
          <div class="d-flex my-4">
            <form
              data-kt-search-element="form"
              class="w-300px position-relative mb-3"
              autocomplete="off">
              <app-keenicon
                name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"
                type="outline">
              </app-keenicon>
              <input
                type="text"
                name="searchText"
                class="form-control form-control-flush ps-10 bg-light border rounded-pill"
                [(ngModel)]="searchText"
                (ngModelChange)="onSearchTextChange($event)"
                placeholder="Search By Unit Type.."
                data-kt-search-element="input"
              />
            </form>
          </div>
          <div class="d-flex my-4">
            <div class="position-relative me-3">
              <a class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer" (click)="toggleFilterDropdown()">
                <i class="fa-solid fa-filter"></i> Filter
              </a>

              <!-- Filter Dropdown -->
              <div *ngIf="isFilterDropdownVisible" class="dropdown-menu show p-3 shadow" style="position: absolute; top: 100%; left: 0; z-index: 1000;">
                <app-model-unit-filter (filtersApplied)="onFiltersApplied($event)"></app-model-unit-filter>
              </div>
            </div>

            <a class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer">
                <i class="fa-solid fa-arrow-down-wide-short"></i>
                Sort
            </a>
            <a
                class="btn btn-sm btn-success cursor-pointer"
                *ngIf="user?.role === 'developer'"
              >
                <i class="bi bi-file-earmark-spreadsheet me-1"></i>
                Download Excel
            </a>
          </div>
        </div>
      </div>
    </div>

    <app-empty-properties-card
      *ngIf="showEmptyCard"
      [userRole]="'developer'"
    ></app-empty-properties-card>

    <div *ngIf="showEmptyCard" class="text-center mb-5">
      <button class="btn btn-primary btn-lg" (click)="uploadModel()">
        <i class="bi bi-file-earmark-spreadsheet me-2"></i>
        Upload Unite Excel Sheet
      </button>
    </div>

    <div
      class="table-responsive mb-5"
      *ngIf="!showEmptyCard && rows.length > 0"
    >
      <table
        class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5"
      >
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="w-25px ps-4 rounded-start">
              <div
                class="form-check form-check-sm form-check-custom form-check-solid"
              >
                <input
                  class="form-check-input"
                  type="checkbox"
                  value="1"
                  data-kt-check="true"
                  data-kt-check-target=".widget-13-check"
                />
              </div>
            </th>
            <th class="min-w-150px">
              Model Code
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
             <th class="min-w-150px">
              Unit
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-150px">
              Building Number
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-200px">
              Apartment Number
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-150px">
              View
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px">
              Floor
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-150px">
              Delivery Date
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-150px">
              Finishing Type
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px">
              Status
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px">
              Diagram
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-200px">
              Location In Master Plan
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-150px">
              Other Accessories
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of rows">
            <td class="ps-4">
              <div
                class="form-check form-check-sm form-check-custom form-check-solid"
              >
                <input
                  class="form-check-input widget-13-check"
                  type="checkbox"
                  value="1"
                />
              </div>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.modelCode }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.type }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.buildingNumber }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.unitNumber }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.view }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.floor }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.deliveryDate }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.finishingType }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.status }}
              </span>
            </td>
            <td>
              <button
                class="btn btn-sm btn-light-info d-flex align-items-center justify-content-center mx-auto"
                (click)="showUnitPlanModal(row.diagram)"
                style="min-width: 120px; white-space: nowrap"
              >
                <i class="fa-solid fa-file-image me-1"></i>
                <span class="d-none d-md-inline">View Diagram</span>
                <span class="d-inline d-md-none">Diagram</span>
              </button>
            </td>
            <td>
              <button
                class="btn btn-sm btn-light-info d-flex align-items-center justify-content-center mx-auto"
                (click)="showUnitPlanModal(row.locationInMasterPlan)"
                style="min-width: 120px; white-space: nowrap"
              >
                <i class="fa-solid fa-file-image me-1"></i>
                <span class="d-none d-md-inline"> View Location</span>
                <span class="d-inline d-md-none">Location In Master Plan</span>
              </button>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.otherAccessories }}
              </span>
            </td>
            <td>
              <button
                class="btn btn-sm btn-primary d-flex align-items-center justify-content-center mx-auto"
                [routerLink]="['/developer/projects/models/units/details']"
                [queryParams]="{ unitId: row.id }"
                style="min-width: 100px; white-space: nowrap"
              >
                <i class="fa-solid fa-eye me-1"></i>
                <span>View Details</span>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<router-outlet></router-outlet>

<!-- View Unit Plan Modal -->
<div
  class="modal fade"
  id="viewUnitPlanModal"
  tabindex="-1"
  aria-labelledby="unitPlanModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="unitPlanModalLabel">View</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body text-center">
        <div *ngIf="selectedUnitPlanImage; else noImage">
          <img
            [src]="selectedUnitPlanImage"
            alt="Unit Diagram"
            class="img-fluid rounded"
            style="max-height: 500px"
          />
          <div class="mt-3">
            <p class="text-muted">Image</p>
          </div>
        </div>
        <ng-template #noImage>
          <div class="alert alert-warning">No available</div>
        </ng-template>
      </div>
    </div>
  </div>
</div>
