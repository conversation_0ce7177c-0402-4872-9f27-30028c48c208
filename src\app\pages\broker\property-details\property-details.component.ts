import {  Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { UnitsService } from '../../developer/services/units.service';


@Component({
  selector: 'app-property-details',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './property-details.component.html',
  styleUrl: './property-details.component.scss',
})
export class PropertyDetailsComponent implements OnInit {


  unitId: number | null = null;
   unitDetails: any;
   isLoading: boolean = false;

   // Carousel properties
   AllImages: any[] = [];
   isAutoRotating: boolean = true;

   // Features mapping
   features = [
     { name: 'Garage', value: 'garage' },
     { name: 'Clubhouse', value: 'clubhouse' },
     { name: 'Club', value: 'club' },
     { name: 'Storage', value: 'storage' },
     { name: 'Elevator', value: 'elevator' },
     { name: 'Swimming Pool', value: 'swimming_pool' }
   ];

   constructor(
     private route: ActivatedRoute,
     private unitsService: UnitsService
   ) {}

   ngOnInit() {
     this.route.queryParams.subscribe((params) => {
       if (params['unitId']) {
         this.unitId = +params['unitId'];
       }
     });
     this.loadUnitDetails();
   }

   loadUnitDetails() {
     if (this.unitId) {
       this.isLoading = true;
       this.unitsService.getUnitById(this.unitId).subscribe({
         next: (response) => {
           console.log(response.data);
           this.unitDetails = response.data;
           console.log(this.unitDetails);
           this.concate(this.unitDetails);
           this.isLoading = false;
         },
         error: (error) => {
           console.error('Error loading unit details:', error);
           this.isLoading = false;
         },
       });
     }
   }

   concate(details: any  ) {


     // Add gallery items if they exist
     if (details && details.gallery && details.gallery.length > 0) {
     this.AllImages = [...details.gallery];
     }

     // Add diagram if it exists
     if (details && details.diagram) {
        this.AllImages.push({
         type: 'image',
         url: details.diagram,

       });
     }

     // Add location in master plan if it exists
     if (details && details.locationInMasterPlan) {
        this.AllImages.push({
         type: 'image',
         url: details.locationInMasterPlan,

       });
     }

   }

   isFeatureEnabled(featureValue: string): boolean {
     return this.unitDetails?.otherAccessories?.includes(featureValue) || false;
   }

}
