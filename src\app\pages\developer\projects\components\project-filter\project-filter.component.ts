import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';

@Component({
  selector: 'app-project-filter',
  templateUrl: './project-filter.component.html',
  styleUrl: './project-filter.component.scss'
})
export class ProjectFilterComponent {

  unitTypes: { key: string; value: string }[] = [];

  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    managementTeam: '',
    projectExecuter: '',
  };

  constructor( private propertyService: PropertyService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {}

  apply() {
    this.filtersApplied.emit(this.filter);
  }

}
