.card {
  max-width: 550px;
  margin: 0 auto;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e4e6ef;

  .card-body {
    padding: 1.5rem;
  }

  &.cursor-pointer {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
  }
}

.text-dark-blue {
  color: #0d6efd; /* More vibrant blue color */
}

// Upload card styling
.upload-card-container {
  .card {
    transition: all 0.2s ease;
    border-radius: 25px;
    border: 1px solid #e4e6ef;

    label {
      cursor: pointer;
      font-size: 1rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 80px;
      margin-bottom: 0;

      .upload-icon {
        width: 32px;
        height: 32px;
        background-color: #0d6efd;
        border-radius: 50%;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          color: white;
          font-size: 16px;
        }
      }

      .upload-text {
        color: #0d6efd;
        font-weight: bold;
      }
    }

    &:hover {
      border-color: #0d6efd;
      box-shadow: 0 0 10px rgba(13, 110, 253, 0.1);
    }
  }
}

.btn-dark-blue {
  background-color: #1e1e2d;
  color: #ffffff;
}

.btn-navy {
  background-color: #1e1e7c;
  color: #ffffff;
  border: none;
  &:hover {
    background-color: #16165a;
  }
  &:disabled {
    background-color: #9999c9;
  }
}

.progress {
  border-radius: 30px;
}

.progress-bar {
  border-radius: 30px;
}

.cursor-pointer {
  cursor: pointer;
}

// Custom styling for the form
.form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

.form-select {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

// Dropdown styling
.dropdown {
  .btn-outline-secondary {
    border-radius: 8px;
    border: 1px solid #e4e6ef;
    background-color: #f5f8fa;
    color: #5e6278;
    padding: 0.75rem 1rem;

    &:hover,
    &:focus {
      background-color: #f5f8fa;
      border-color: #e4e6ef;
    }

    &::after {
      display: none;
    }
  }

  .dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
    padding: 0.5rem 0;

    .dropdown-item {
      padding: 0.75rem 1.25rem;
      cursor: pointer;

      &:hover {
        background-color: #f5f8fa;
      }
    }
  }
}

.cities-dropdown {
  max-height: 300px;
  overflow-y: auto;
}

.cities-list {
  max-height: 200px;
  overflow-y: auto;

  li {
    margin: 0;
    padding: 0;
  }

  .dropdown-item {
    padding: 0.5rem 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: #f8f9fa;
    }
  }
}

// Custom scrollbar styling
.cities-list::-webkit-scrollbar {
  width: 6px;
}

.cities-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cities-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;

  &:hover {
    background: #555;
  }
}

// Ensure the dropdown menu has proper padding
.dropdown-menu {
  padding: 0.5rem 0;
  margin: 0;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.areas-dropdown {
  max-height: 300px;
  overflow-y: auto;
}

.areas-list {
  max-height: 200px;
  overflow-y: auto;

  li {
    margin: 0;
    padding: 0;
  }

  .dropdown-item {
    padding: 0.5rem 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: #f8f9fa;
    }
  }
}

// Custom scrollbar styling for areas
.areas-list::-webkit-scrollbar {
  width: 6px;
}

.areas-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.areas-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;

  &:hover {
    background: #555;
  }
}
