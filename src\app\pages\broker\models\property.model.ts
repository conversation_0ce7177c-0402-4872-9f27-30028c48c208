export interface PropertyData {
  // Step 1: Location Information
  unitType: string;
  city: string;
  area: string;
  neighborhoodNumber: string;
  detailedAddress: string;
  googleMapsLink: string;

  // Step 2: Unit Information
  propertyNumber: string;
  apartmentNumber: string;
  floor: string;
  apartmentArea: string | number;
  roomsCount: string | number;
  bathroomsCount: string | number;
  apartmentLocation: string;
  apartmentView: string;
  finishingStatus: string;
  deliveryStatus: string;
  additionalAmenities: string;

  // Step 3: Property Information
  projectType: string;
  apartmentsCount: number;
  villasCount: number;
  duplexesCount: number;

  // Step 4: Project Documents
  projectLogo: File[] | string;
  projectLayout: File[] | string;
  projectLicenses: File[] | string;
  projectImages: File[] | string;
  projectVideos: File[] | string;
  price: number | null;
  isNegotiable: boolean;
}
