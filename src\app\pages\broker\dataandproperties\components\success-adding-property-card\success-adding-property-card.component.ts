import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-success-adding-property-card',
  templateUrl: './success-adding-property-card.component.html',
  styleUrl: './success-adding-property-card.component.scss',
})
export class SuccessAddingPropertyCardComponent {
  @Output() backToTable = new EventEmitter<void>();

  onBackToTable() {
    this.backToTable.emit();
  }
}
