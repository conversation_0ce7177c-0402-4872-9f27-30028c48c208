import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-empty-properties-card',
  templateUrl: './empty-properties-card.component.html',
  styleUrl: './empty-properties-card.component.scss',
})
export class EmptyPropertiesCardComponent {
  @Input() userRole: string ;
  @Input() customMessage: string = '';
  @Input() onFileUpload!: (file: File) => void;
  @Input() onDownloadTemplate!: () => void;

  get displayMessage(): string {
    if (this.customMessage) {
      return this.customMessage;
    }

    switch (this.userRole) {
      case 'broker':
        return "Create a new property or upload the property units you're working on.";
      case 'developer':
        return 'Create a new project or start building your development portfolio.';
      default:
        return "Create a new property or upload the property units you're working on.";
    }
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file && this.onFileUpload) {
      this.onFileUpload(file);
    }
  }

  downloadTemplate() {
    if (this.onDownloadTemplate) {
      this.onDownloadTemplate();
    }
  }
}
