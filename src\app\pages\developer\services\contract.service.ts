import { Injectable } from '@angular/core';
import { AbstractCrudService } from '../../shared/services/abstract-crud.service';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { Page } from 'src/app/models/page.model';

@Injectable({
  providedIn: 'root',
})
export class ContractService extends AbstractCrudService {
  apiUrl = `${environment.apiUrl}/broker/request`;

  createContractRequest(formData: FormData): Observable<any> {
    return this.http.post(this.apiUrl, formData);
  }

  getContractRequests(page: Page): Observable<any> {
    const offset = page.size * page.pageNumber;

    const params = {
      limit: page.size,
      offset: offset,
      sort: page.orderDir,
      sortBy: page.orderBy,
      ...page.filters,
    };

    return this.http.get<any[]>(`${environment.apiUrl}/broker/request`, {
      params,
    });
  }

  approveRequest(requestId: number): Observable<any> {
    return this.http.put(`${this.apiUrl}/${requestId}`, { status: 'accepted' });
  }

  rejectRequest(requestId: number): Observable<any> {
    return this.http.put(`${this.apiUrl}/${requestId}`, { status: 'declined' });
  }
}
