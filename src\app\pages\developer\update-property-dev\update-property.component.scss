.card {
  max-width: 550px;
  margin: 0 auto;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e4e6ef;

  .card-body {
    padding: 1.5rem;
  }

  &.cursor-pointer {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
  }
}

.text-dark-blue {
  color: #0d6efd; /* More vibrant blue color */
}

// Upload card styling
.upload-card-container {
  .card {
    transition: all 0.2s ease;
    border-radius: 25px;
    border: 1px solid #e4e6ef;

    label {
      cursor: pointer;
      font-size: 1rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 80px;
      margin-bottom: 0;

      .upload-icon {
        width: 32px;
        height: 32px;
        background-color: #0d6efd;
        border-radius: 50%;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          color: white;
          font-size: 16px;
        }
      }

      .upload-text {
        color: #0d6efd;
        font-weight: bold;
      }
    }

    &:hover {
      border-color: #0d6efd;
      box-shadow: 0 0 10px rgba(13, 110, 253, 0.1);
    }
  }
}

.btn-dark-blue {
  background-color: #1e1e2d;
  color: #ffffff;
}

.btn-navy {
  background-color: #1e1e7c;
  color: #ffffff;
  border: none;
  &:hover {
    background-color: #16165a;
  }
  &:disabled {
    background-color: #9999c9;
  }
}

.btn-blue-custom {
  background-color: #007bff;
  color: #ffffff;
  border: none;
  &:hover {
    background-color: #0056b3;
  }
  &:disabled {
    background-color: #6c757d;
    color: #ffffff;
  }
}

.btn-green-custom {
  background-color: #ffffff;
  color: #000000;
  border: 2px solid #28a745;
  &:hover {
    background-color: #28a745;
    color: #ffffff;
    border-color: #28a745;
  }
  &:focus {
    background-color: #ffffff;
    color: #000000;
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  }
}

.progress {
  border-radius: 30px;
}

.progress-bar {
  border-radius: 30px;
}

.cursor-pointer {
  cursor: pointer;
}

// Custom styling for the form
.form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

.form-select {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

// Dropdown styling
.dropdown {
  .btn-outline-secondary {
    border-radius: 8px;
    border: 1px solid #e4e6ef;
    background-color: #f5f8fa;
    color: #5e6278;
    padding: 0.75rem 1rem;

    &:hover,
    &:focus {
      background-color: #f5f8fa;
      border-color: #e4e6ef;
    }

    &::after {
      display: none;
    }
  }

  .dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
    padding: 0.5rem 0;

    .dropdown-item {
      padding: 0.75rem 1.25rem;
      cursor: pointer;

      &:hover {
        background-color: #f5f8fa;
      }
    }
  }
}

// Existing files display styling
.existing-files-container {
  background-color: #f8f9fa;
  border-top: 1px solid #e4e6ef;

  h6 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #5e6278;
    margin-bottom: 0.75rem;
  }

  .file-item {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);

      .delete-btn {
        opacity: 1;
      }
    }

    img {
      border: 2px solid #e4e6ef;
      transition: all 0.2s ease;

      &:hover {
        border-color: #0d6efd;
      }
    }

    .delete-btn {
      opacity: 0;
      transition: all 0.2s ease;
      width: 5px;
      height: 5px;
      padding: 0;

      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        transform: scale(1.1);
      }
    }

    .video-overlay-small {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      pointer-events: none;
    }
  }
}

// Badge styling for file counts
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.5rem;

  &.bg-info {
    background-color: #0dcaf0 !important;
  }

  &.bg-success {
    background-color: #198754 !important;
  }
}
