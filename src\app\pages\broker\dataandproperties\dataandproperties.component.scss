// Simple filter styling
.form-select {
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  }
}

.form-label {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.btn-light-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  transition: all 0.15s ease-in-out;

  &:hover {
    background-color: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.3);
    color: #dc3545;
  }
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  transition: all 0.15s ease-in-out;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    background: #6c757d;
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  i {
    font-size: 1rem;
  }
}

.btn-outline-danger {
  border: 2px solid #dc3545;
  color: #ffffff;
  background: rgba(47, 175, 151, 0.414);
  transition: all 0.15s ease-in-out;
  border-radius: 0.5rem;

  &:hover {
    background: #dc3545;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
  }

  i {
    font-size: 1rem;
  }
}

// Card styling
.card {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e3ea;

  .card-body {
    padding: 1.5rem;
  }
}

// Text colors
.text-dark-blue {
  color: #2d3748 !important;
}


// Responsive adjustments
@media (max-width: 768px) {
  .filter-buttons {
    .btn {
      font-size: 0.875rem;
      padding: 0.5rem 0.75rem;
    }
  }
}
