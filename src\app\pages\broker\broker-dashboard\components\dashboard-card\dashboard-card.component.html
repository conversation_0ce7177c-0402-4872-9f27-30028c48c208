<div class="card card-custom card-stretch mb-5 bg-hover-light-primary">
  <div class="card-header border-0 pt-5 pb-5 d-flex align-items-center justify-content-between">
    <h3 class="card-title align-items-start flex-column">
      <span class="card-label fw-bolder fs-3 mb-1 text-dark-blue">
        <!-- Font Awesome Icon -->
        <ng-container *ngIf="isFontAwesomeIcon()">
          <i class="fa-regular fa-{{ icon.name }} text-dark-blue fs-3"></i>
        </ng-container>

        <!-- Keen Icon -->
        <ng-container *ngIf="isKeenIcon()">
          <app-keenicon
            [name]="getKeenIconName()"
            [type]="getKeenIconType()"
            class="fs-1 fw-semibold text-dark-blue"
          ></app-keenicon>
        </ng-container>

        <!-- Custom SVG Icon -->
        <ng-container *ngIf="isSvgIcon()">
          <span [innerHTML]="getSafeSvgContent()" class="fs-3"></span>
        </ng-container>

        {{ title }}
      </span>

      <span class="text-muted mt-1 fw-bold fs-7">
        {{ subTitle }}
      </span>
    </h3>
    <div class="card-toolbar">
      <a routerLink="{{ buttonLink }}" class="btn btn-sm btn-light-dark-blue">
        {{ buttonTitle }}
        <i class="fa-solid fa-{{ buttonIcon }} fs-7"></i>
      </a>
    </div>
  </div>
</div>
