import { Injectable } from '@angular/core';
import { AbstractCrudService } from '../../shared/services/abstract-crud.service';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class UnitService extends AbstractCrudService {
  apiUrl = `${environment.apiUrl}/unit`;

  getByBrokerId(brokerId: number): Observable<any> {
    const params = new HttpParams()
      .set('limit', '50')
      .set('offset', '0')
      .set('sort', 'DESC')
      .set('sortBy', 'id')
      .set('brokerId', brokerId.toString());

    return this.http.get<any>(this.apiUrl, { params });
  }

  downloadExcelTemplate(): Observable<Blob> {
    const url = `${environment.apiUrl}/unit/download-excel-units-models`;
    return this.http.get(url, {
      responseType: 'blob',
    });
  }

  uploadExcelUnits(file: File, id: any): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('id', id);
    const url = `${environment.apiUrl}/unit/import-excel-units-models`;
    return this.http.post(url, formData);
  }
}
