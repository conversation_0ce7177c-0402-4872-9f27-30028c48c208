<div class="filter-dropdown">
  <div class="mb-2">
     <label class="form-label">No of Rooms:</label>
    <input
      type="number"
      class="form-control form-control-sm"
      [(ngModel)]="filter.noOfRooms"
      min="1"
      step="1"
      placeholder="Enter number of rooms"
    />
  </div>

  <div class="mb-2">
   <label class="form-label">No of Bathrooms:</label>
    <input
      type="number"
      class="form-control form-control-sm"
      [(ngModel)]="filter.noOfBathrooms"
      min="1"
      step="1"
      placeholder="Enter number of bathrooms"
    />
  </div>

  <button class="btn btn-sm btn-primary w-100" (click)="apply()">Apply</button>
</div>
