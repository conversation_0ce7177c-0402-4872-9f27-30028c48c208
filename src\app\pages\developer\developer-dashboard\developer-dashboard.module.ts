import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';
import { NgApexchartsModule } from 'ng-apexcharts';
import { FormsModule } from '@angular/forms';
import {
  BaseChartDirective,
  provideCharts,
  withDefaultRegisterables,
} from 'ng2-charts';

import { HeaderComponent } from './components/header/header.component';
import { AnalysisCardComponent } from './components/analysis-card/analysis-card.component';
import { DeveloperDashboardComponent } from './developer-dashboard.component';
import { ChartComponent } from './components/chart/chart.component';
import { ProjectPieChartComponent } from './components/project-pie-chart/project-pie-chart.component';
import { ContractRequestsChartComponent } from './components/contract-requests-chart/contract-requests-chart.component';

@NgModule({
  declarations: [
    DeveloperDashboardComponent,
    HeaderComponent,
    AnalysisCardComponent,
    ChartComponent,
    ProjectPieChartComponent,
    ContractRequestsChartComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    SharedModule,
    NgApexchartsModule,
    FormsModule,
    BaseChartDirective,
  ],
  providers: [provideCharts(withDefaultRegisterables())],
  exports: [
    DeveloperDashboardComponent,
    HeaderComponent,
    AnalysisCardComponent,
    ChartComponent,
    ProjectPieChartComponent,
  ],
})
export class DeveloperDashboardModule {}
