import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SuccessAddingPropertyCardComponent } from './success-adding-property-card.component';

describe('SuccessAddingPropertyCardComponent', () => {
  let component: SuccessAddingPropertyCardComponent;
  let fixture: ComponentFixture<SuccessAddingPropertyCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SuccessAddingPropertyCardComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SuccessAddingPropertyCardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
