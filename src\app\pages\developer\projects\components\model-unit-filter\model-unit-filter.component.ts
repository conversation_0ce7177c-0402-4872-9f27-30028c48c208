import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';

@Component({
  selector: 'app-model-unit-filter',
  templateUrl: './model-unit-filter.component.html',
  styleUrl: './model-unit-filter.component.scss'
})
export class ModelUnitFilterComponent {

  unitTypes: { key: string; value: string }[] = [];

  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    finishingType: '',
    status: '',
    unitType:'',
  };

  constructor( private propertyService: PropertyService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.loadUnitTypes();
  }

  finishingTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

 status: { key: string; value: string }[] = [
    { key: 'NEW', value: 'new' },
    { key: 'AVAILABLE', value: 'available' },
    { key: 'RESERVED', value: 'reserved' },
    { key: 'SOLD', value: 'sold' },
  ];

  // toggleDropdown() {
  //   this.isOpen = !this.isOpen;
  // }

  apply() {
    // this.isOpen = false;
    this.filtersApplied.emit(this.filter);
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.unitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        console.log('Raw API Response:', this.unitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  // loadCities(): void {
  //   this.propertyService.getCities().subscribe({
  //     next: (response) => {
  //       if (response && response.data) {
  //         this.cities = response.data;
  //       } else {
  //         console.warn('No cities data in response');
  //         this.cities = [];
  //       }
  //     },
  //     error: (err) => {
  //       console.error('Error loading cities:', err);
  //     },
  //     complete: () => {
  //       this.cdr.detectChanges();
  //     },
  //   });
  // }

  // loadAreas(cityId?: number): void {
  //   this.propertyService.getAreas(cityId).subscribe({
  //     next: (response) => {
  //       if (response && response.data) {
  //         this.areas = response.data;
  //       } else {
  //         console.warn('No areas data in response');
  //         this.areas = [];
  //       }
  //     },
  //     error: (err) => {
  //       console.error('Error loading areas:', err);
  //       this.areas = [];
  //     },
  //     complete: () => {
  //       this.cdr.detectChanges();
  //     },
  //   });
  // }

}
