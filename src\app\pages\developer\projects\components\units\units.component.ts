import { ChangeDetectorRef, Component } from '@angular/core';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';

import { ActivatedRoute } from '@angular/router';
import { Modal } from 'bootstrap';
import { UnitsService } from '../../../services/units.service';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-units',
  templateUrl: './units.component.html',
  styleUrl: './units.component.scss',
})
export class UnitsComponent extends BaseGridComponent {
  user: any = { role: 'developer' };
  showEmptyCard = false;
  appliedFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;
  isFilterDropdownVisible = false;


  constructor(
    protected cd: ChangeDetectorRef,
    protected unitService: UnitsService,
    private activatedRoute: ActivatedRoute
  ) {
    super(cd);
    this.setService(unitService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit() {
    super.ngOnInit();
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params['modelCode']) {
        this.page.filters = { modelCode: params['modelCode'] };
      }
    });
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout); // Clear previous timeout

    this.searchTimeout = setTimeout(() => {
      this.page.filters = {...this.page.filters, unitType: value.trim()};
      this.reloadTable(this.page);
    }, 300);
  }

   toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  onFiltersApplied(filters: any) {
    this.toggleFilterDropdown();
    this.page.filters = {...this.page.filters, ...filters};

    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any): Promise<void> {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.loading = true;
    await this._service.getAll(this.page).subscribe(
      (pagedData: any) => {
        console.log(pagedData.data);
        this.rows = Array.isArray(pagedData.data)? pagedData.data : [];
        this.rows = [...this.rows];

        this.page.totalElements = pagedData.count;
        this.page.count = Math.ceil(pagedData.count / this.page.size);

        // Update empty card visibility
        this.updateEmptyCardVisibility();

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
        MenuComponent.reinitialization();
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    )
  }

  updateEmptyCardVisibility() {
    // Show empty card when there are no units
    this.showEmptyCard = this.rows.length === 0;
  }

  selectedUnitPlanImage: string;
  showUnitPlanModal(diagramUrl: string) {
    this.selectedUnitPlanImage = diagramUrl;
    const modalElement = document.getElementById('viewUnitPlanModal');
    if (modalElement) {
      const modal = new Modal(modalElement);
      modal.show();
    }
  }

  uploadModel() {
    console.log('Upload model clicked');
  }
}
