import { Injectable } from '@angular/core';
import { AbstractCrudService } from '../../shared/services/abstract-crud.service';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class PropertyService extends AbstractCrudService {
  apiUrl = `${environment.apiUrl}/unit`;

  constructor(protected override http: HttpClient) {
    super(http);
  }

  getUnitTypes(): Observable<any> {
    return this.http.get(`${environment.apiUrl}/unit/unit-types`, {});
  }

  getCities(
    cityId?: number,
    limit: number = 100,
    offset: number = 0,
    sort: string = 'asc',
    sortBy: string = 'id'
  ): Observable<any> {
    const params: any = {
      limit,
      offset,
      sort,
      sortBy,
    };

    if (cityId) {
      params['filters[cityId]'] = cityId;
    }

    return this.http.get(`${environment.apiUrl}/location/city`, { params });
  }

  getAreas(
    cityId?: number,
    limit: number = 100,
    offset: number = 0,
    sort: string = 'asc',
    sortBy: string = 'id'
  ): Observable<any> {
    const params: any = {
      limit,
      offset,
      sort,
      sortBy,
    };

    if (cityId) {
      params['cityId'] = cityId;
    }

    return this.http.get(`${environment.apiUrl}/location/area`, { params });
  }

  gitsubAreas(
    areaId?: number,
    limit: number = 100,
    offset: number = 0,
    sort: string = 'asc',
    sortBy: string = 'id'
  ): Observable<any> {
    const params: any = {
      limit,
      offset,
      sort,
      sortBy,
    };

    if (areaId) {
      params['areaId'] = areaId;
    }

    return this.http.get(`${environment.apiUrl}/location/sub-area`, { params });
  }

  createProperty(formData: FormData): Observable<any> {
    return this.http.post(`${this.apiUrl}/create-unit`, formData);
  }
}
