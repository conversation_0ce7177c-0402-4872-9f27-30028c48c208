import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseGridComponent } from '../../shared/base-grid/base-grid.component';
import { ProjectsService } from '../services/projects.service';
import Swal from 'sweetalert2';
import { MenuComponent } from 'src/app/_metronic/kt/components';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrl: './projects.component.scss',
})
export class ProjectsComponent extends BaseGridComponent implements OnInit {
  user: any;

  showEmptyCard = false;
  appliedFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;
  isFilterDropdownVisible = false;

  constructor(
    protected cd: ChangeDetectorRef,
    private projectsService: ProjectsService
  ) {
    super(cd);
    this.setService(projectsService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit() {
    super.ngOnInit();
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;
    this.page.filters = {developerId : this.user.developerId};
  }

  updateCardVisibility() {
    this.showEmptyCard = this.rows.length === 0;
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout); // Clear previous timeout
    this.searchTimeout = setTimeout(() => {
      this.page.filters = {...this.page.filters, searchName: value.trim()};
      console.log(this.page.filters);
      this.reloadTable(this.page);
    }, 300);
  }

  toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  onFiltersApplied(filters: any) {
    this.toggleFilterDropdown();
    this.page.filters = {...this.page.filters, ...filters};

    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.loading = true;
    await this._service.getAll(this.page).subscribe(
      (response: any) => {
        console.log(response.data);
        this.rows = Array.isArray(response.data) ? response.data : [];
        this.rows = [...this.rows];

        this.page.totalElements = response.count;
        this.page.count = Math.ceil(response.count / this.page.size);

        this.cd.markForCheck();
        this.loading = false;

        this.updateCardVisibility();

        this.afterGridLoaded();

        setTimeout(() => {
          MenuComponent.reinitialization();
        }, 150);
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    );
  }

}
