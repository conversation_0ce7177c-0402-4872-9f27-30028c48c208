:host {
  display: block;
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-image: url("/angular/assets/media/login/EaseDealPage.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;
  position: relative;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .auth-layout {
    background-position: center left;
  }
}

@media (max-width: 768px) {
  .auth-layout {
    padding: 10px;
    justify-content: center;
    background-position: center;
  }
}
