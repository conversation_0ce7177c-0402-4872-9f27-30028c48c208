import { Component, Input } from '@angular/core';
import { SharedModule } from '../../../../../_metronic/shared/shared.module';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-new-request-card',
  templateUrl: './new-request-card.component.html',
  styleUrl: './new-request-card.component.scss',
})
export class NewRequestCardComponent {

  @Input() title: string = '';
  @Input() subTitle: string = '';
  @Input() icon: { type: 'fontawesome' | 'keenicon' | 'svg', name?: string, iconType?: string, svgContent?: string } = { type: 'fontawesome' };
  @Input() buttonTitle: string = '';
  @Input() buttonIcon: string = '';
  @Input() buttonLink: string = '';

    constructor(private sanitizer: DomSanitizer) {}

  // Sanitize SVG content to prevent XSS
  getSafeSvgContent(): SafeHtml {
    return this.icon.svgContent ? this.sanitizer.bypassSecurityTrustHtml(this.icon.svgContent) : '';
  }

  // Check if icon is Font Awesome
  isFontAwesomeIcon(): boolean {
    return this.icon.type === 'fontawesome';
  }

  // Check if icon is Keen
  isKeenIcon(): boolean {
    return this.icon.type === 'keenicon';
  }

  // Check if icon is custom SVG
  isSvgIcon(): boolean {
    return this.icon.type === 'svg';
  }
}
