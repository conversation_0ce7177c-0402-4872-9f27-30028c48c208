<div class="client-registration-stepper">
  <!-- Stepper Header -->
  <div class="stepper-header">
    <h2 class="stepper-title">Broker Registration</h2>
    <div class="stepper-progress">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="(currentStep / totalSteps) * 100"></div>
      </div>
      <div class="d-flex">
        <span class="progress-text">Step {{ currentStep }} of {{ totalSteps }}</span>
        <button *ngIf="currentStep > 0 && currentStep < 8" type="button" class="back-to-previous"
          (click)="previousStep()">
          Back to previous step
        </button>
      </div>
    </div>
  </div>

  <!-- Stepper Content -->
  <form [formGroup]="registrationForm" class="stepper-form">
    <!-- Step 1: Basic Information -->
    <div *ngIf="currentStep === 1" class="step-content">
      <h3 class="step-title">Enter Your Basic Information</h3>

      <div class="form-group">
        <label for="fullName" class="form-label">
          <i class="ki-outline ki-user"></i>
          Full Name <span class="required"></span>
        </label>
        <input type="text" id="fullName" formControlName="fullName" class="form-control"
          [class.is-invalid]="isFieldInvalid('fullName')" placeholder="Enter full name..." pattern="[^0-9]*"
          title="Name cannot contain numbers" (blur)="markFieldAsTouched('fullName')" required />
        <div *ngIf="isFieldInvalid('fullName')" class="invalid-feedback">
          {{ getFieldError("fullName") }}
        </div>
      </div>

      <div class="form-group">
        <label for="input" class="form-label">
          <i class="ki-outline ki-phone"></i>
          Enter Email or Phone Number <span class="required"></span>
        </label>
        <input type="text" id="input" formControlName="input" class="form-control"
          [class.is-invalid]="isFieldInvalid('input')" placeholder="<EMAIL> or 01123928909"
          title="Enter a valid email address or phone number" (blur)="markFieldAsTouched('input')" required />
        <div *ngIf="isFieldInvalid('input')" class="invalid-feedback">
          {{ getFieldError("input") }}
        </div>
      </div>

      <!-- Send Verification Button -->
      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingSendOtp"
        [disabled]="!isStep1Valid() || isLoadingSendOtp" (click)="handleNextStepAndSendCode()">
        <span *ngIf="isLoadingSendOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
        {{ isLoadingSendOtp ? 'Sending...' : 'Send Verification Code' }}
      </button>

      <!-- Help Text -->
      <div class="help-text">
        Need help? <span class="contact-link">Contact us</span>
      </div>
    </div>

    <!-- Step 2: Verification Code -->
    <div *ngIf="currentStep === 2" class="step-content">
      <h3 class="step-title">Enter Verification Code</h3>

      <!-- Verification Code Input -->
      <div class="verification-code-section">
        <div formArrayName="verificationCode" class="verification-inputs">
          <div class="code-input" *ngFor="let ctrl of verificationCodeControls; let i = index">
            <input type="text" maxlength="1" class="verification-input" [formControlName]="i"
              [class.is-invalid]="ctrl.invalid && ctrl.touched" (input)="autoFocusNext($event, i)" />
          </div>
        </div>
      </div>

      <!-- Countdown Timer -->
      <div class="countdown-section">
        <span class="countdown-text" *ngIf="!showResendButton">
          Resend in
          <span class="countdown-timer">
            0:{{ countdown < 10 ? "0" + countdown : countdown }} </span>
          </span>

          <button *ngIf="showResendButton" class="btn btn-link" (click)="onResendCode()">
            Resend Code
          </button>
      </div>

      <!-- OTP Error Message -->
      <div *ngIf="otpErrorMessage" class="alert alert-danger mt-3" role="alert">
        {{ otpErrorMessage }}
      </div>

      <!-- Next Button -->
      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingCheckOtp"
        [disabled]="!isStep2Valid() || isLoadingCheckOtp" (click)="checkOTP()">
        <span *ngIf="isLoadingCheckOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
        {{ isLoadingCheckOtp ? 'Verifying...' : 'Verified - Next' }}
      </button>

      <!-- Help Text -->
      <div class="help-text">
        Need help? <span class="contact-link">Contact us</span>
      </div>
    </div>

    <!-- Step 3: Broker Type Selection -->
    <div *ngIf="currentStep === 3" class="step-content">
      <h3 class="step-title">Choose Broker Type</h3>

      <div class="broker-type-section">
        <p class="broker-type-description">
          Please select the type of broker registration you want to proceed with
        </p>

        <!-- Broker Type Options -->
        <div class="broker-type-options">
          <!-- Independent Broker -->
          <div class="broker-type-card" [class.selected]="brokerType === 'independent'"
            (click)="selectBrokerType('independent')">
            <div class="broker-type-icon">
              <i class="ki-outline ki-user"></i>
            </div>
            <h4 class="broker-type-title">Independent Broker</h4>
            <p class="broker-type-desc">
              Register as an individual real estate broker
            </p>
          </div>

          <!-- Real Estate Company -->
          <div class="broker-type-card" [class.selected]="brokerType === 'real_estate_brokage_company'"
            (click)="selectBrokerType('real_estate_brokage_company')">
            <div class="broker-type-icon">
              <i class="ki-outline ki-office-bag"></i>
            </div>
            <h4 class="broker-type-title">Real Estate Company</h4>
            <p class="broker-type-desc">
              Register as a real estate brokerage company
            </p>
          </div>
        </div>

        <!-- Continue Button -->
        <button type="button" class="btn btn-primary btn-verification" [disabled]="!brokerType" (click)="nextStep()">
          Continue
        </button>

        <!-- Help Text -->
        <div class="help-text">
          Need help? <span class="contact-link">Contact us</span>
        </div>
      </div>
    </div>

    <!-- Step 4: Documents Upload -->
    <div *ngIf="currentStep === 4" class="step-content">
      <h3 class="step-title">Please Upload Required Documents</h3>

      <div class="documents-section">
        <p class="documents-description">
          You can upload the required documents now or skip and add them later
          when you first use the required services
        </p>

        <!-- Document Upload Cards -->
        <div class="upload-card-container">
          <div *ngIf="brokerType === 'independent'">
            <!-- Profile Photo -->
            <div class="card mb-3 cursor-pointer">
              <label for="image" class="card-body text-center py-2">
                <div class="upload-icon cursor-pointer">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text cursor-pointer">
                  Profile Photo
                  <span *ngIf="getFileCount('image') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("image") }}
                  </span>
                </span>
                <div class="upload-subtitle">PNG, JPG</div>
                <input type="file" id="image" class="d-none" (change)="onFileChange($event, 'image')"
                  accept=".png,.jpg,.jpeg" />
              </label>
            </div>

            <!-- ID Document -->
            <div class="card mb-3 cursor-pointer">
              <label for="idFront" class="card-body text-center py-2">
                <div class="upload-icon cursor-pointer">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text cursor-pointer">
                  National ID Front
                  <span *ngIf="getFileCount('idFront') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("idFront") }}
                  </span>
                </span>
                <div class="upload-subtitle">PNG, JPG, PDF</div>
                <input type="file" id="idFront" class="d-none" (change)="onFileChange($event, 'idFront')"
                  accept=".png,.jpg,.jpeg,.pdf" multiple />
              </label>
            </div>

            <!-- License Document -->
            <div class="card mb-3 cursor-pointer">
              <label for="idBack" class="card-body text-center py-2">
                <div class="upload-icon cursor-pointer">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text cursor-pointer">
                  National ID Back
                  <span *ngIf="getFileCount('idBack') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("idBack") }}
                  </span>
                </span>
                <div class="upload-subtitle">PNG, JPG, PDF</div>
                <input type="file" id="idBack" class="d-none" (change)="onFileChange($event, 'idBack')"
                  accept=".png,.jpg,.jpeg,.pdf" multiple />
              </label>
            </div>
          </div>

          <!-- Company Documents   -->
          <div *ngIf="brokerType === 'real_estate_brokage_company'">
            <!-- Company Logo -->
            <div class="card mb-3 cursor-pointer">
              <label for="image" class="card-body text-center py-2">
                <div class="upload-icon cursor-pointer">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text cursor-pointer">
                  Company logo image for account
                  <span *ngIf="getFileCount('image') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("image") }}
                  </span>
                </span>
                <div class="upload-subtitle">PNG, JPG</div>
                <input type="file" id="image" class="d-none" (change)="onFileChange($event, 'image')"
                  accept=".png,.jpg,.jpeg" />
              </label>
            </div>

            <!-- Commercial Registration -->
            <div class="card mb-3 cursor-pointer">
              <label for="commercialRegistryImage" class="card-body text-center py-2">
                <div class="upload-icon cursor-pointer">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text cursor-pointer">
                  Commercial register photo
                  <span *ngIf="getFileCount('commercialRegistryImage') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("commercialRegistryImage") }}
                  </span>
                </span>
                <div class="upload-subtitle">PNG, JPG, PDF</div>
                <input type="file" id="commercialRegistryImage" class="d-none"
                  (change)="onFileChange($event, 'commercialRegistryImage')" accept=".png,.jpg,.jpeg,.pdf" multiple />
              </label>
            </div>

            <!-- Tax Card -->
            <div class="card mb-3 cursor-pointer">
              <label for="taxCardImage" class="card-body text-center py-2">
                <div class="upload-icon cursor-pointer">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text cursor-pointer">
                  Tax card image
                  <span *ngIf="getFileCount('taxCardImage') > 0" class="badge bg-success ms-2">
                    {{ getFileCount("taxCardImage") }}
                  </span>
                </span>
                <div class="upload-subtitle">PNG, JPG, PDF</div>
                <input type="file" id="taxCardImage" class="d-none" (change)="onFileChange($event, 'taxCardImage')"
                  accept=".png,.jpg,.jpeg,.pdf" multiple />
              </label>
            </div>
          </div>
        </div>

        <!-- Upload Documents Button -->
        <button type="button" class="btn btn-primary btn-verification" (click)="nextStep()">
          Upload Documents
        </button>

        <!-- Skip Button -->
        <button type="button" class="btn btn-link skip-button" (click)="nextStep()">
          Skip and return later
          <i class="ki-outline ki-arrow-right"></i>
        </button>

        <!-- Help Text -->
        <div class="help-text">
          Need help? <span class="contact-link">Contact us</span>
        </div>
      </div>
    </div>

    <!-- Step 5: Choose Your Areas -->
    <div *ngIf="currentStep === 5" class="step-content compact-step">
      <h3 class="step-title">Choose Your Work Areas</h3>

      <div class="areas-section">
        <!-- City Selection -->
        <div class="form-group">
          <label class="form-label">City</label>
          <select class="form-control" [value]="selectedCityId" (change)="onCityChange($event)">
            <option value="">Select City</option>
            <option *ngFor="let city of cities" [value]="city.id">
              {{ city.name_en }}
            </option>
          </select>
        </div>

        <!-- Area Selection -->
        <div class="form-group">
          <label class="form-label">Area</label>
          <select class="form-control" [value]="selectedAreaId" (change)="onAreaChange($event)"
            [disabled]="!selectedCityId">
            <option value="">Select Area</option>
            <option *ngFor="let area of areas" [value]="area.id">
              {{ area.name_en }}
            </option>
          </select>
        </div>

        <!-- subArea Selection -->
        <div class="form-group">
          <label class="form-label">sub Area</label>
          <select class="form-control" [value]="selectedSubAreaId" (change)="onSubAreaChange($event)"
            [disabled]="!selectedAreaId">
            <option value="">Select Sub Area</option>
            <option *ngFor="let subarea of subAreas" [value]="subarea.id">{{ subarea.name_en }}</option>
          </select>
        </div>

        <!-- Continue Button -->
        <button type="button" class="btn btn-primary btn-verification" (click)="nextStep()">
          Continue to Specializations
        </button>

        <!-- Skip Button -->
        <button type="button" class="btn btn-link skip-button" (click)="nextStepSkipping()">
          Skip and return later
          <i class="ki-outline ki-arrow-right"></i>
        </button>

        <!-- Help Text -->
        <div class="help-text">
          Need help? <span class="contact-link">Contact us</span>
        </div>
      </div>
    </div>

    <!-- Step 6: Choose Specializations -->
    <div *ngIf="currentStep === 6" class="step-content compact-step">
      <h3 class="step-title">Choose Your Specializations</h3>

      <div class="areas-section">
        <!-- Specializations -->
        <div class="form-group">
          <div class="specialization-collapse">
            <div *ngFor="let scope of staticScopes" class="specialization-item">
              <div class="specialization-header" [class.disabled]="isOtherScopeExpanded(scope)">
                <label class="scope-checkbox-label">
                  <input type="checkbox" [checked]="scope.selected" (change)="onScopeSelectionChange(scope, $event)"
                    class="scope-checkbox" [disabled]="isOtherScopeExpanded(scope)" />
                  <span class="specialization-name">{{
                    scope.specialization_scope
                    }}</span>
                </label>
                <i [class]="
                    scope.expanded ? 'ki-outline ki-up' : 'ki-outline ki-down'
                  " class="collapse-icon" (click)="toggleSpecializationScope(scope)"
                  [class.disabled]="isOtherScopeExpanded(scope)"></i>
              </div>
              <div *ngIf="scope.expanded" class="specialization-content">
                <div class="specialization-checkboxes">
                  <label *ngFor="let type of scope.specializations" class="checkbox-label">
                    <input type="checkbox" [checked]="scope.selectedTypes?.includes(type)"
                      (change)="onSpecializationChange(scope, type, $event)">
                    <span class="checkbox-text">{{ type }}</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Continue Button -->
        <button type="button" class="btn btn-primary btn-verification" (click)="nextStep()">
          Continue to Account Setup
        </button>

        <!-- Skip Button -->
        <button type="button" class="btn btn-link skip-button" (click)="nextStep()">
          Skip and return later
          <i class="ki-outline ki-arrow-right"></i>
        </button>

        <!-- Help Text -->
        <div class="help-text">
          Need help? <span class="contact-link">Contact us</span>
        </div>
      </div>
    </div>

    <!-- Step 7: Username and Password -->
    <div *ngIf="currentStep === 7" class="step-content">
      <h3 class="step-title">Enter Your Account Details</h3>

      <!-- Phone -->
      <div class="form-group">
        <label for="phone" class="form-label">
          <i class="ki-outline ki-phone"></i>
          Phone <span class="required"></span>
        </label>
        <input type="tel" id="phone" formControlName="phone" class="form-control"
          [class.is-invalid]="isFieldInvalid('phone')" placeholder="01xxxxxxxxx" required autocomplete="tel"
          (blur)="markFieldAsTouched('phone')" />
        <div *ngIf="isFieldInvalid('phone')" class="invalid-feedback">
          {{ getFieldError("phone") }}
        </div>
      </div>

      <!-- Email -->
      <div class="form-group">
        <label for="email" class="form-label">
          <i class="ki-outline ki-user"></i>
          Email
        </label>
        <input type="email" id="email" formControlName="email" class="form-control"
          [class.is-invalid]="isFieldInvalid('email')" placeholder="<EMAIL>..." autocomplete="email"
          (blur)="markFieldAsTouched('email')" />
        <div *ngIf="isFieldInvalid('email')" class="invalid-feedback">
          {{ getFieldError("email") }}
        </div>
      </div>

      <!-- Password -->
      <div class="form-group">
        <label for="password" class="form-label">
          <i class="ki-outline ki-lock"></i>
          Password <span class="required"></span>
        </label>
        <input type="password" id="password" formControlName="password" class="form-control"
          [class.is-invalid]="isFieldInvalid('password')" placeholder="********" minlength="8"
          pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$"
          title="Password must be at least 8 characters with uppercase, lowercase and number" required
          autocomplete="new-password" (blur)="markFieldAsTouched('password')" />
        <div *ngIf="isFieldInvalid('password')" class="invalid-feedback">
          {{ getFieldError("password") }}
        </div>
      </div>

      <!--Confirm Password -->
      <div class="form-group">
        <label for="confirmPassword" class="form-label">
          <i class="ki-outline ki-lock"></i>
          Confirm Password <span class="required"></span>
        </label>
        <input type="password" id="confirmPassword" formControlName="password_confirmation" class="form-control"
          [class.is-invalid]="isFieldInvalid('password_confirmation') || getFormError()" placeholder="********" required
          autocomplete="new-password" (blur)="markFieldAsTouched('password_confirmation')" />
        <div *ngIf="isFieldInvalid('password_confirmation')" class="invalid-feedback">
          {{ getFieldError("password_confirmation") }}
        </div>
        <div *ngIf="getFormError()" class="invalid-feedback">
          {{ getFormError() }}
        </div>
      </div>

      <!-- Terms Agreement -->
      <div class="form-group">
        <div class="form-check">
          <input type="checkbox" id="agreeTerms" formControlName="agreeTerms" class="form-check-input"
            [class.is-invalid]="isFieldInvalid('agreeTerms')" />
          <label for="agreeTerms" class="form-check-label">
            I agree to the Terms and Conditions <span class="required"></span>
          </label>
        </div>
        <div *ngIf="isFieldInvalid('agreeTerms')" class="invalid-feedback">
          {{ getFieldError("agreeTerms") }}
        </div>
      </div>

      <!-- Create Account Error Message -->
      <div *ngIf="createAccountErrorMessage" class="alert alert-danger mt-3" role="alert">
        {{ createAccountErrorMessage }}
      </div>

      <!-- Create Account Button -->
      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingCreateAccount"
        [disabled]="!isStep7Valid() || isLoadingCreateAccount" (click)="createAccount()">
        <span *ngIf="isLoadingCreateAccount" class="spinner-border spinner-border-sm me-2" role="status"></span>
        {{ isLoadingCreateAccount ? 'Creating Account...' : 'Create Account' }}
      </button>

      <!-- Help Text -->
      <div class="help-text">
        Need help? <span class="contact-link">Contact us</span>
      </div>
    </div>

    <!-- Step 8: Success Page -->
    <div *ngIf="currentStep === 8" class="step-content success-step">
      <div class="success-content">
        <div class="success-icon">
          <i class="ki-outline ki-check-circle"></i>
        </div>

        <h3 class="success-title">Registration Successful</h3>

        <p class="success-message">
          Your account has been successfully created. You can now enjoy the
          various and amazing services provided by Easy Deal through the website
          or dashboard.
        </p>

        <div class="success-illustration">
          <img src="/assets/media/login/successfully.png" alt="Success" class="success-image" />
        </div>

        <button type="button" class="btn btn-primary btn-success-action" [routerLink]="['/broker/dashboard']">
          Go to Dashboard
        </button>

        <div class="additional-info">
          <span class="info-link">Learn all about your account and how to get started</span>
        </div>
      </div>
    </div>
  </form>
</div>