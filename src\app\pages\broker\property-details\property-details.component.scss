.cursor-pointer {
  cursor: pointer;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
}

.badge {
  &.bg-light-primary {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
  }

  &.bg-light-success {
    background-color: rgba(var(--bs-success-rgb), 0.1) !important;
  }
}

.form-check-input:disabled {
  opacity: 0.7;
}

.form-check-label {
  font-size: 0.875rem;
}

// Image hover effects
.position-relative img {
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .col-lg-8,
  .col-lg-4 {
    margin-bottom: 1rem;
  }

  .card-body {
    padding: 1rem;
  }
}

// Loading spinner
.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Custom button styles
.btn {
  border-radius: 0.5rem;
  font-weight: 500;

  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      transform: translateY(-1px);
    }
  }
}

// Price styling
.text-success {
  color: #28a745 !important;
  font-size: 1.5rem;
}

// Icon styling
.fa-solid {
  &.text-primary {
    color: #667eea !important;
  }
}

// Unit image container styling
.unit-image-container {
  height: 400px;
  border-radius: 0.5rem;
  overflow: hidden;
}

.unit-image-background {
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.upload-section {
  color: #6c757d;

  .upload-icon {
    margin-bottom: 1rem;
  }

  h5 {
    color: #0d6efd !important;
    font-weight: 600;
  }

  p {
    font-size: 0.875rem;
    color: #6c757d !important;
  }
}

.unit-info-overlay {
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);

  h2 {
    font-size: 1.75rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  p {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

// Pink badge styling
.bg-pink {
  background-color: #e91e63 !important;
}

// Property details text styling
.property-details-text {
  p {
    line-height: 1.6;
    color: #495057;
    font-size: 0.95rem;
  }
}
