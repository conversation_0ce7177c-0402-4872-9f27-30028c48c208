<div class="mb-20 mt-0">
  <app-developer-header
    [title]="'project'"
    [subtitle]="'To view existing projects, edit them, and add new projects'"
  >
  </app-developer-header>
</div>

<div class="card rounded-4">
  <div class="card-body p-10">
    <div
      class="stepper stepper-pills d-flex flex-column"
      id="add_property_stepper"
    >
      <!-- Header and Progress Bar -->
      <div class="mb-5 text-center">
        <ng-container *ngIf="currentStep === 1">
          <h2>
            <span class="text-dark-blue fw-bold">Add Project - </span>
            <span class="text-dark-blue fw-normal">Basic Data</span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 2">
          <h2>
            <span class="text-dark-blue fw-bold">Add Project - </span>
            <span class="text-dark-blue fw-normal">Location </span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 3">
          <h2>
            <span class="text-dark-blue fw-bold">Add Project - </span>
            <span class="text-dark-blue fw-normal"> Project type</span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 4">
          <h2>
            <span class="text-dark-blue fw-bold">Add Project - </span>
            <span class="text-dark-blue fw-normal">Project Documents</span>
          </h2>
        </ng-container>

        <div class="d-flex justify-content-center align-items-center mb-2">
          <span class="text-success fw-bold">Step {{ currentStep }}</span>
          <span class="text-muted mx-1">of</span>
          <span class="text-muted">{{ totalSteps }}</span>
        </div>

        <div
          *ngIf="currentStep > 1"
          class="text-primary cursor-pointer mb-2"
          (click)="prevStep()"
        >
          Back to previous step
        </div>

        <div class="progress h-8px bg-light-success w-75 mx-auto">
          <div
            class="progress-bar bg-success"
            role="progressbar"
            [style.width]="(currentStep / totalSteps) * 100 + '%'"
            aria-valuenow="50"
            aria-valuemin="0"
            aria-valuemax="100"
          ></div>
        </div>
      </div>

      <!-- Form Content -->
      <form class="mx-auto w-100 pt-5 pb-10">
        <!-- Step 1: Basic Property Settings -->
        <div *ngIf="currentStep === 1" [formGroup]="step1Form">
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Project name
            </label>
            <input
              type="text"
              class="form-control text-start"
              [class.is-invalid]="hasFieldError('name')"
              formControlName="name"
            />
            <div *ngIf="hasFieldError('name')" class="invalid-feedback d-block">
              {{ getFieldErrorMessage("name") }}
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Project Designer
            </label>
            <input
              type="text"
              class="form-control text-start"
              [class.is-invalid]="hasFieldError('designer')"
              formControlName="designer"
            />
            <div
              *ngIf="hasFieldError('designer')"
              class="invalid-feedback d-block"
            >
              {{ getFieldErrorMessage("designer") }}
            </div>
          </div>
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Project implementer
            </label>
            <input
              type="text"
              class="form-control text-start"
              [class.is-invalid]="hasFieldError('projectExecutor')"
              formControlName="projectExecutor"
            />
            <div
              *ngIf="hasFieldError('projectExecutor')"
              class="invalid-feedback d-block"
            >
              {{ getFieldErrorMessage("projectExecutor") }}
            </div>
          </div>
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Project management
            </label>
            <input
              type="text"
              class="form-control text-start"
              [class.is-invalid]="hasFieldError('managementTeam')"
              formControlName="managementTeam"
            />
            <div
              *ngIf="hasFieldError('managementTeam')"
              class="invalid-feedback d-block"
            >
              {{ getFieldErrorMessage("managementTeam") }}
            </div>
          </div>
        </div>

        <!-- Step 2: Location Information -->
        <div *ngIf="currentStep === 2" [formGroup]="step2Form">
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"> City</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="cityDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span> {{ selectedCityName || "Select City" }} </span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="cityDropdownStep1"
                style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                "
              >
                <!-- Debug info -->
                <li class="dropdown-item disabled">
                  Total - Cities: {{ cities.length }}
                </li>

                <ng-container
                  *ngIf="cities && cities.length > 0; else noCities"
                >
                  <li *ngFor="let city of cities" style="cursor: pointer">
                    <a
                      class="dropdown-item text-start"
                      (click)="selectCity(city.id, city.name_en)"
                    >
                      {{ city.name_en }}
                    </a>
                  </li>
                </ng-container>

                <ng-template #noCities>
                  <li>
                    <a class="dropdown-item text-start disabled">
                      No cities available
                    </a>
                  </li>
                </ng-template>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Area</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="areaDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{ selectedAreaName || "Select Area" }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="areaDropdownStep1"
                style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                "
              >
                <li *ngIf="areas.length > 0">
                  <a
                    *ngFor="let area of areas"
                    class="dropdown-item text-start"
                    (click)="selectArea(area.id, area.name_en)"
                    >{{ area.name_en }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Details address</label
            >
            <input
              type="text"
              class="form-control text-start"
              [class.is-invalid]="hasFieldError('address')"
              formControlName="address"
              placeholder=" Enter the address in details"
            />
            <div
              *ngIf="hasFieldError('address')"
              class="invalid-feedback d-block"
            >
              {{ getFieldErrorMessage("address") }}
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Website link on Google Maps</label
            >
            <input
              type="text"
              class="form-control text-start"
              [class.is-invalid]="hasFieldError('googleMapUrl')"
              formControlName="googleMapUrl"
              placeholder=" Enter the map link"
            />
            <div
              *ngIf="hasFieldError('googleMapUrl')"
              class="invalid-feedback d-block"
            >
              {{ getFieldErrorMessage("googleMapUrl") }}
            </div>
          </div>
        </div>

        <!-- Step 3: Project type   -->
        <div *ngIf="currentStep === 3" [formGroup]="step3Form">
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Project type
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="projectTypeDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  step3Form.get("projectType")?.value || "commercial..."
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="projectTypeDropdown"
              >
                <li>
                  <a
                    class="dropdown-item text-start"
                    (click)="selectProjectType(' residential')"
                  >
                    residential</a
                  >
                </li>
                <li>
                  <a
                    class="dropdown-item text-start"
                    (click)="selectProjectType('commercial')"
                    >commercial</a
                  >
                </li>
                <li>
                  <a
                    class="dropdown-item text-start"
                    (click)="selectProjectType('mixed')"
                    >mixed</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Number of project units</label
            >

            <!-- Buildings Row -->
            <div class="d-flex mb-3 align-items-center">
              <button
                type="button"
                class="btn btn-primary me-3"
                style="width: 150px; background-color: #1e1e7c"
              >
                Buildings
              </button>
              <div class="flex-grow-1">
                <input
                  type="number"
                  class="form-control text-start"
                  [class.is-invalid]="hasFieldError('buildingsCount')"
                  formControlName="buildingsCount"
                  placeholder="00"
                />
                <div
                  *ngIf="hasFieldError('buildingsCount')"
                  class="invalid-feedback d-block"
                >
                  {{ getFieldErrorMessage("buildingsCount") }}
                </div>
              </div>
            </div>

            <!-- Apartments Row -->
            <div class="d-flex mb-3 align-items-center">
              <button
                type="button"
                class="btn btn-primary me-3"
                style="width: 150px; background-color: #1e1e7c"
              >
                Apartments
              </button>
              <div class="flex-grow-1">
                <input
                  type="number"
                  class="form-control text-start"
                  [class.is-invalid]="hasFieldError('apartmentsCount')"
                  formControlName="apartmentsCount"
                  placeholder="00"
                />
                <div
                  *ngIf="hasFieldError('apartmentsCount')"
                  class="invalid-feedback d-block"
                >
                  {{ getFieldErrorMessage("apartmentsCount") }}
                </div>
              </div>
            </div>

            <!-- Villas Row -->
            <div class="d-flex mb-3 align-items-center">
              <button
                type="button"
                class="btn btn-primary me-3"
                style="width: 150px; background-color: #1e1e7c"
              >
                Villas
              </button>
              <div class="flex-grow-1">
                <input
                  type="number"
                  class="form-control text-start"
                  [class.is-invalid]="hasFieldError('villasCount')"
                  formControlName="villasCount"
                  placeholder="00"
                />
                <div
                  *ngIf="hasFieldError('villasCount')"
                  class="invalid-feedback d-block"
                >
                  {{ getFieldErrorMessage("villasCount") }}
                </div>
              </div>
            </div>

            <!-- Duplexes Row -->
            <div class="d-flex mb-3 align-items-center">
              <button
                type="button"
                class="btn btn-primary me-3"
                style="width: 150px; background-color: #1e1e7c"
              >
                Duplexes
              </button>
              <div class="flex-grow-1">
                <input
                  type="number"
                  class="form-control text-start"
                  [class.is-invalid]="hasFieldError('duplexCount')"
                  formControlName="duplexCount"
                  placeholder="00"
                />
                <div
                  *ngIf="hasFieldError('duplexCount')"
                  class="invalid-feedback d-block"
                >
                  {{ getFieldErrorMessage("duplexCount") }}
                </div>
              </div>
            </div>

            <!-- administrative units count Row -->
            <div class="d-flex mb-3 align-items-center">
              <button
                type="button"
                class="btn btn-primary me-3"
                style="width: 150px; background-color: #1e1e7c"
              >
                Administrative Units
              </button>
              <div class="flex-grow-1">
                <input
                  type="number"
                  class="form-control text-start"
                  [class.is-invalid]="hasFieldError('administrativeUnitsCount')"
                  formControlName="administrativeUnitsCount"
                  placeholder="00"
                />
                <div
                  *ngIf="hasFieldError('administrativeUnitsCount')"
                  class="invalid-feedback d-block"
                >
                  {{ getFieldErrorMessage("administrativeUnitsCount") }}
                </div>
              </div>
            </div>

            <!-- commercial units count Row-->
            <div class="d-flex mb-3 align-items-center">
              <button
                type="button"
                class="btn btn-primary me-3"
                style="width: 150px; background-color: #1e1e7c"
              >
                Commercial Units
              </button>
              <div class="flex-grow-1">
                <input
                  type="number"
                  class="form-control text-start"
                  [class.is-invalid]="hasFieldError('commercialUnitsCount')"
                  formControlName="commercialUnitsCount"
                  placeholder="00"
                />
                <div
                  *ngIf="hasFieldError('commercialUnitsCount')"
                  class="invalid-feedback d-block"
                >
                  {{ getFieldErrorMessage("commercialUnitsCount") }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4: Project Documents -->
        <div *ngIf="currentStep === 4" [formGroup]="step4Form">
          <!-- Project Documents Cards -->
          <div class="mb-10 upload-card-container">
            <!-- Project Logo -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectLogo" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project logo
                  <!-- Show new files count -->
                  <span
                    *ngIf="getFileCount('logoImage') > 0"
                    class="badge bg-success ms-2"
                    title="New files selected"
                  >
                    {{ getFileCount("logoImage") }}
                  </span>
                </span>
                <input
                  type="file"
                  id="projectLogo"
                  class="d-none"
                  (change)="onFileChange($event, 'logoImage')"
                  multiple
                />
              </label>
            </div>

            <!-- Project Layout -->
            <div class="card mb-5 cursor-pointer">
              <label for="coverImage" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project Cover
                  <span
                    *ngIf="getFileCount('coverImage') > 0"
                    class="badge bg-success ms-2"
                    title="New files selected"
                  >
                    {{ getFileCount("coverImage") }}
                  </span>
                </span>
                <input
                  type="file"
                  id="coverImage"
                  class="d-none"
                  (change)="onFileChange($event, 'coverImage')"
                  multiple
                />
              </label>
            </div>

            <!-- Project Licenses -->
            <div class="card mb-5 cursor-pointer">
              <label for="masterPlan" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project Master Plan
                  <span
                    *ngIf="getFileCount('masterPlan') > 0"
                    class="badge bg-success ms-2"
                    title="New files selected"
                  >
                    {{ getFileCount("masterPlan") }}
                  </span>
                </span>
                <input
                  type="file"
                  id="masterPlan"
                  class="d-none"
                  (change)="onFileChange($event, 'masterPlan')"
                  multiple
                />
              </label>
            </div>

            <!-- Project Images -->
            <div class="card mb-5 cursor-pointer">
              <label for="gallery" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project images
                  <!-- Show new files count -->
                  <span
                    *ngIf="getFileCount('gallery') > 0"
                    class="badge bg-success ms-2"
                    title="New files selected"
                  >
                    {{ getFileCount("gallery") }}
                  </span>
                </span>
                <input
                  type="file"
                  id="gallery"
                  class="d-none"
                  (change)="onFileChange($event, 'gallery')"
                  multiple
                />
              </label>
            </div>

            <!-- Project Videos -->
            <div class="card mb-5 cursor-pointer">
              <label for="videos" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project videos
                  <!-- Show new files count -->
                  <span
                    *ngIf="getFileCount('videos') > 0"
                    class="badge bg-success ms-2"
                    title="New files selected"
                  >
                    {{ getFileCount("videos") }}
                  </span>
                </span>
                <input
                  type="file"
                  id="videos"
                  class="d-none"
                  (change)="onFileChange($event, 'videos')"
                  accept="video/*"
                  multiple
                />
              </label>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons -->

        <div
          *ngIf="currentStep === 1"
          class="d-flex justify-content-between pt-10"
        >
          <button
            type="button"
            class="btn btn-light-dark btn-lg px-6 py-3"
            (click)="cancel()"
          >
            Cancel
          </button>

          <button
            type="button"
            class="btn btn-lg btn-navy px-10 py-3 rounded-pill"
            [disabled]="!isCurrentFormValid()"
            (click)="nextStep()"
          >
            <span class="indicator-label text-white">
              Next - Location Information
            </span>
          </button>
        </div>

        <!-- ******-->
        <div
          *ngIf="currentStep > 1"
          class="d-flex justify-content-center pt-10"
        >
          <ng-container *ngIf="currentStep !== totalSteps">
            <button
              type="button"
              class="btn btn-lg btn-navy px-10 py-3 rounded-pill"
              [disabled]="!isCurrentFormValid()"
              (click)="nextStep()"
            >
              <span class="indicator-label text-white">
                <ng-container *ngIf="currentStep === 2">
                  Next - Project type
                </ng-container>
                <ng-container *ngIf="currentStep === 3">
                  Next - Project Documents
                </ng-container>
              </span>
            </button>
          </ng-container>

          <ng-container *ngIf="currentStep === totalSteps">
            <div class="d-flex flex-column align-items-center">
              <button
                type="button"
                class="btn btn-lg btn-blue-custom px-10 py-3 rounded-pill mb-3 w-100"
                [disabled]="!isCurrentFormValid()"
                (click)="submitForm()"
              >
                <span class="indicator-label text-white">
                  Create a new project
                </span>
              </button>

              <button
                type="button"
                class="btn btn-lg btn-green-custom px-10 py-3 rounded-pill w-100"
                style="border: 2px solid #28a745"
                (click)="submitForm()"
              >
                <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                <span class="indicator-label"
                  >Create and upload units file</span
                >
              </button>
            </div>
          </ng-container>
        </div>
      </form>
    </div>
  </div>
</div>
