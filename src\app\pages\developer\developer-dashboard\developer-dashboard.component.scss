.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.dashboard-content {
  padding: 20px;
  overflow-y: auto;
}

.dashboard-cards-container {
  margin-top: 20px;
}

.project-title,
.project-title-left {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

.project-title-left {
  .text-dark {
    font-size: 1rem;
  }

  i {
    font-size: 1.1rem;
  }
}

.main-dashboard-container {
  display: flex;
  flex-direction: row;
  gap: 1px;
}

.analysis-cards-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  flex: 55%;
}

.card {
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: none;

  &.bg-primary,
  &.bg-success,
  &.bg-danger,
  &.bg-warning {
    .card-header {
      border-bottom: none;
    }
  }
}

.badge {
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

.fs-7 {
  font-size: 0.85rem !important;
}

.fs-2 {
  font-size: 1.75rem !important;
  font-weight: 600;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.bullet {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

@media (max-width: 1200px) {
  .col-xl-4,
  .col-xl-8 {
    width: 100%;
    margin-bottom: 20px;
  }

  .col-xl-6 {
    width: 100%;
    margin-bottom: 20px;
  }

  .main-dashboard-container {
    flex-direction: column;
  }

  .analysis-cards-container {
    flex: 1 1 100%;
  }

  .dashboard-pie-chart {
    flex: 1 1 100%;
    justify-content: center;
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start !important;

    .card-toolbar {
      margin-top: 10px;
      width: 100%;
    }
  }

  .analysis-cards-container {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
  }
}

// Top 5 Models Styles
.top-models-section {
  .model-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

      .hover-overlay {
        opacity: 0.05 !important;
      }
    }
  }

  .rank-badge {
    z-index: 2;

    &.rank-1 {
      background: linear-gradient(135deg, #f6c000, #dead00);
      box-shadow: 0 4px 15px rgba(246, 192, 0, 0.3);
    }

    &.rank-2 {
      background: linear-gradient(135deg, #2e8bc0, #2779a9);
      box-shadow: 0 4px 15px rgba(46, 139, 192, 0.3);
    }

    &.rank-3 {
      background: linear-gradient(135deg, #0d47a1, #0b3c8c);
      box-shadow: 0 4px 15px rgba(13, 71, 161, 0.3);
    }
  }

  .decorative-pattern {
    opacity: 0.08;
    color: var(--bs-primary);
    transition: all 0.3s ease;
  }

  .model-details {
    .symbol {
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  .rating {
    .fa-star {
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.2);
      }
    }
  }

  .empty-state {
    .symbol {
      animation: pulse 2s infinite;
    }
  }
}

// Animation keyframes
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Hover effects
.hover-overlay {
  transition: opacity 0.3s ease;
  pointer-events: none;
}

// Responsive adjustments for top models
@media (max-width: 992px) {
  .top-models-section {
    .model-card {
      margin-bottom: 1rem;
    }
  }
}

@media (max-width: 768px) {
  .top-models-section {
    .card-body {
      padding: 1rem;
    }

    .model-details {
      .symbol {
        width: 35px;
        height: 35px;

        i {
          font-size: 1rem !important;
        }
      }
    }

    .rank-badge {
      font-size: 0.8rem !important;
      padding: 0.4rem 0.8rem !important;
    }
  }
}
