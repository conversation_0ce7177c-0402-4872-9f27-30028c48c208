.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.dashboard-content {
  padding: 20px;
  overflow-y: auto;
}

.dashboard-cards-container {
  margin-top: 20px;
}

 .project-title,
.project-title-left {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

.project-title-left {
  .text-dark {
    font-size: 1rem;
  }

  i {
    font-size: 1.1rem;
  }
}

 .main-dashboard-container {
  display: flex;
  flex-direction: row;
  gap: 1px;
}

 .analysis-cards-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  flex: 55%;
}



 .card {
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: none;

  &.bg-primary,
  &.bg-success,
  &.bg-danger,
  &.bg-warning {
    .card-header {
      border-bottom: none;
    }
  }
}

 .badge {
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

 .fs-7 {
  font-size: 0.85rem !important;
}

.fs-2 {
  font-size: 1.75rem !important;
  font-weight: 600;
}

.opacity-75 {
  opacity: 0.75 !important;
}

 .bullet {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

 @media (max-width: 1200px) {
  .col-xl-4,
  .col-xl-8 {
    width: 100%;
    margin-bottom: 20px;
  }

  .col-xl-6 {
    width: 100%;
    margin-bottom: 20px;
  }

  .main-dashboard-container {
    flex-direction: column;
  }

  .analysis-cards-container {
    flex: 1 1 100%;
  }

  .dashboard-pie-chart {
    flex: 1 1 100%;
    justify-content: center;
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start !important;

    .card-toolbar {
      margin-top: 10px;
      width: 100%;
    }
  }

  .analysis-cards-container {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
  }
}
