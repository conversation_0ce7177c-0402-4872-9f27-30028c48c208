import { ChangeDetectorRef, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import Swal from 'sweetalert2';
import { BrokerService } from '../services/broker.service';
interface FileItem {
  id: number;
  description: string;
  type: string;
  fileUrl: string;
}

@Component({
  selector: 'app-my-maps',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './my-maps.component.html',
  styleUrl: './my-maps.component.scss',
})
export class MyMapsComponent {
  files: FileItem[] = [];
  selectedFile: File | null = null;
  fileDescription: string = '';
  filePreviewUrl: string | ArrayBuffer | null = null;
  selectedImageIndex: number = 0;

  brokerId: any;

  constructor(
    private modalService: NgbModal,
    protected cd: ChangeDetectorRef,
    protected brokerService: BrokerService
  ) {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.brokerId = user?.brokerId;
    this.getAllImages(this.brokerId);
  }

  openUploadModal(content: any) {
    this.selectedFile = null;
    this.fileDescription = '';
    this.filePreviewUrl = null;
    this.modalService.open(content, { centered: true });
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files?.[0]) {
      this.selectedFile = input.files[0];
      const reader = new FileReader();
      reader.onload = () => (this.filePreviewUrl = reader.result);
      reader.readAsDataURL(this.selectedFile);
    }
  }

  uploadFile() {
    if (this.selectedFile && this.fileDescription) {
      const formData = new FormData();
      formData.append('image', this.selectedFile);
      formData.append('description', this.fileDescription);

      this.brokerService.uploadImage(this.brokerId, formData).subscribe(
        (response: any) => {
          console.log(response.data.data);
          Swal.fire('file uploaded successfully', '', 'success');
          this.cd.markForCheck();
          this.getAllImages(this.brokerId);
        },
        (error: any) => {
          console.log(error);
          this.cd.markForCheck();
          Swal.fire('Failed to upload. please try again later.', '', 'error');
        }
      );
      this.modalService.dismissAll();
    }
  }

  openImageGallery(content: any, index: number) {
    const fileId = this.files[index].id;
    this.selectedImageIndex = Math.max(
      0,
      this.files.findIndex((file) => file.id === fileId)
    );

    this.modalService.open(content, {
      centered: true,
      size: 'xl',
      fullscreen: 'lg',
      windowClass: 'image-gallery-modal',
    });
  }

  nextImage() {
    this.selectedImageIndex = (this.selectedImageIndex + 1) % this.files.length;
  }

  prevImage() {
    this.selectedImageIndex =
      this.selectedImageIndex > 0
        ? this.selectedImageIndex - 1
        : this.files.length - 1;
  }

  async getAllImages(brokerId: any) {
    await this.brokerService.getImages(brokerId).subscribe(
      (response: any) => {
        this.files = response.data.data;
        this.cd.markForCheck();
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    );
  }
}
