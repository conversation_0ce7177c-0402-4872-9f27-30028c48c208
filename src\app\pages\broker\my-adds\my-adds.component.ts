import { ChangeDetectorRef, Component, HostBinding, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BaseGridComponent } from '../../shared/base-grid/base-grid.component';
import { PropertyService } from '../services/property.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { PaginationComponent } from "../../../pagination/pagination.component";

@Component({
  selector: 'app-my-adds',
  standalone: true,
  imports: [CommonModule, PaginationComponent],
  templateUrl: './my-adds.component.html',
  styleUrl: './my-adds.component.scss',
})
export class MyAddsComponent extends BaseGridComponent {
  // Track current image index for each post
  currentImageIndexes: { [postId: number]: number } = {};

  // Media modal properties
  selectedMediaUrl: string | null = null;
  selectedMediaType: string = 'image';
  safeVideoUrl: SafeResourceUrl | null = null;
  modalMediaItems: any[] = [];
  currentModalIndex: number = 0;

  constructor(
    protected cd: ChangeDetectorRef,
    protected propertyService: PropertyService,
    private modalService: NgbModal,
    private sanitizer: DomSanitizer,
    private router: Router
  ) {
    super(cd);
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;

    this.setService(propertyService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
    this.page.filters = { brokerId: user?.brokerId, isAdvertisement: 1 };
  }

  // Open media modal
  openMediaModal(content: any, post: any, event?: Event): void {
    // Stop event propagation to prevent card click
    if (event) {
      event.stopPropagation();
    }

    console.log('Opening modal for post:', post.id);

    // Get all media items and set current index
    this.modalMediaItems = this.getAllImagesFromGallery(post);
    this.currentModalIndex = this.getCurrentImageIndex(post);

    // Set current media item
    this.updateModalMedia();

    // Open the modal
    this.modalService.open(content, {
      centered: true,
      size: 'lg',
      windowClass: 'media-modal',
      backdrop: 'static',
    });

    console.log(
      'Modal opened with:',
      this.selectedMediaType,
      this.selectedMediaUrl
    );
  }

  // Get all media items from gallery
  getAllImagesFromGallery(post: any): any[] {
    const allImages: any[] = [];

    // Add gallery items if they exist
    if (post.gallery && post.gallery.length > 0) {
      const mediaItems = post.gallery.filter((item: any) => item.url);
      allImages.push(...mediaItems);
    }

    // Add diagram if it exists
    if (post.diagram) {
      console.log('Adding diagram to images:', post.diagram);
      allImages.push({
        url: post.diagram,
        type: 'image'
      });
    }

    // Add location in master plan if it exists
    if (post.locationInMasterPlan) {
      console.log('Adding locationInMasterPlan to images:', post.locationInMasterPlan);
      allImages.push({
        url: post.locationInMasterPlan,
        type: 'image'
      });
    }

    console.log('Total images for post', post.id, ':', allImages.length);

    // Return default image if no images found
    if (allImages.length === 0) {
      return [{ url: '/assets/media/auth/404-error.png', type: 'image' }];
    }

    return allImages;
  }

  // Get current media URL for display
  getImageFromGallery(post: any): string {
    const mediaItems = this.getAllImagesFromGallery(post);
    const index = this.getCurrentImageIndex(post);
    const currentItem = mediaItems[index];

    return currentItem.url;
  }

  // Check if current item is a video
  isCurrentItemVideo(post: any): boolean {
    const mediaItems = this.getAllImagesFromGallery(post);
    const index = this.getCurrentImageIndex(post);
    return mediaItems[index].type === 'video';
  }

  // Get video URL if current item is a video
  getCurrentVideoUrl(post: any): string | null {
    const mediaItems = this.getAllImagesFromGallery(post);
    const index = this.getCurrentImageIndex(post);
    const currentItem = mediaItems[index];

    return currentItem.type === 'video' ? currentItem.url : null;
  }

  // Check if post has multiple images
  hasMultipleImages(post: any): boolean {
    return this.getAllImagesFromGallery(post).length > 1;
  }

  // Get current image index for a post
  getCurrentImageIndex(post: any): number {
    if (!this.currentImageIndexes[post.id]) {
      this.currentImageIndexes[post.id] = 0;
    }
    return this.currentImageIndexes[post.id];
  }

  // Navigate to next image
  nextImage(post: any, event: Event): void {
    event.stopPropagation();
    const images = this.getAllImagesFromGallery(post);
    this.currentImageIndexes[post.id] =
      (this.getCurrentImageIndex(post) + 1) % images.length;
    this.cd.markForCheck();
  }

  // Navigate to previous image
  prevImage(post: any, event: Event): void {
    event.stopPropagation();
    const images = this.getAllImagesFromGallery(post);
    const currentIndex = this.getCurrentImageIndex(post);
    this.currentImageIndexes[post.id] =
      currentIndex > 0 ? currentIndex - 1 : images.length - 1;
    this.cd.markForCheck();
  }

  viewPropertyDetails(unitService: any) {
      this.router.navigate(['/developer/projects/models/units/details'], {
        queryParams: { unitId: unitService.id }
      });

  }
  // Update modal media based on current index
  updateModalMedia(): void {
    if (
      this.modalMediaItems.length > 0 &&
      this.currentModalIndex >= 0 &&
      this.currentModalIndex < this.modalMediaItems.length
    ) {
      const currentItem = this.modalMediaItems[this.currentModalIndex];
      this.selectedMediaUrl = currentItem.url;
      this.selectedMediaType = currentItem.type;

      // Create a safe URL for video content
      if (this.selectedMediaType === 'video' && this.selectedMediaUrl) {
        this.safeVideoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
          this.selectedMediaUrl
        );
      } else {
        this.safeVideoUrl = null;
      }
    }
  }

  // Navigate to next media in modal
  nextModalMedia(): void {
    if (this.modalMediaItems.length > 1) {
      this.currentModalIndex =
        (this.currentModalIndex + 1) % this.modalMediaItems.length;
      this.updateModalMedia();
    }
  }

  // Navigate to previous media in modal
  prevModalMedia(): void {
    if (this.modalMediaItems.length > 1) {
      this.currentModalIndex =
        this.currentModalIndex > 0
          ? this.currentModalIndex - 1
          : this.modalMediaItems.length - 1;
      this.updateModalMedia();
    }
  }

  // Check if modal has multiple media items
  hasMultipleModalMedia(): boolean {
    return this.modalMediaItems.length > 1;
  }
}
