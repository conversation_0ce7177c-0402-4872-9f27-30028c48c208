import { Component, OnInit, Input } from '@angular/core';
import { ChartConfiguration } from 'chart.js';

@Component({
  selector: 'app-chart',
  templateUrl: './chart.component.html',
  styleUrls: ['./chart.component.scss'],
})
export class ChartComponent implements OnInit {
  @Input() chartType: 'compound' | 'non-compound' = 'compound';
  
  public selectedTimePeriod: string = '7';

  public barChartOptions: ChartConfiguration<'bar'>['options'] = {
    responsive: true,
    scales: {
      x: {},
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          stepSize: 30,
        },
      },
    },
  };

  public barChartLabels = ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'];

  public barChartData = [
    {
      data: [40, 55, 58, 52, 60, 57],
      label: 'Actual',
      backgroundColor: '#3b82f6',
    },
    {
      data: [70, 80, 93, 91, 77, 95],
      label: 'Target',
      backgroundColor: '#e5e7eb',
    },
  ];

  // Data for different time periods
  private chartDataMap = {
    '7': {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      datasets: [
        {
          data: [40, 45, 50, 55, 60, 65, 70],
          label: 'Actual',
          backgroundColor: '#3b82f6',
        },
        {
          data: [60, 65, 70, 75, 80, 85, 90],
          label: 'Target',
          backgroundColor: '#e5e7eb',
        },
      ],
    },
    '14': {
      labels: ['Week 1', 'Week 2'],
      datasets: [
        {
          data: [50, 60],
          label: 'Actual',
          backgroundColor: '#3b82f6',
        },
        {
          data: [70, 85],
          label: 'Target',
          backgroundColor: '#e5e7eb',
        },
      ],
    },
    '30': {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
      datasets: [
        {
          data: [45, 50, 55, 60],
          label: 'Actual',
          backgroundColor: '#3b82f6',
        },
        {
          data: [65, 70, 80, 90],
          label: 'Target',
          backgroundColor: '#e5e7eb',
        },
      ],
    },
    '90': {
      labels: ['Jan', 'Feb', 'Mar'],
      datasets: [
        {
          data: [40, 55, 58],
          label: 'Actual',
          backgroundColor: '#3b82f6',
        },
        {
          data: [70, 80, 93],
          label: 'Target',
          backgroundColor: '#e5e7eb',
        },
      ],
    },
  };

  get chartTitle(): string {
    return this.chartType === 'compound' ? 'Compound' : 'Non-Compound';
  }

  ngOnInit(): void {
    this.updateChartData();
  }

  onTimePeriodChange(): void {
    this.updateChartData();
  }

  private updateChartData(): void {
    const period = this.selectedTimePeriod as '7' | '14' | '30' | '90';
    const data = this.chartDataMap[period];
    if (data) {
      this.barChartLabels = data.labels;
      this.barChartData = data.datasets;
    }
  }
}
