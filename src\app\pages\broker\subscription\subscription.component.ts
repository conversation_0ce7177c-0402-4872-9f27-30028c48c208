import { SubscriptionService } from './../services/subscription.service';
import { ChangeDetectorRef, Component } from '@angular/core';
import { BaseGridComponent } from '../../shared/base-grid/base-grid.component';

@Component({
  selector: 'app-subscription',
  templateUrl: './subscription.component.html',
  styleUrl: './subscription.component.scss'
})

export class SubscriptionComponent extends BaseGridComponent{

  constructor(protected cd : ChangeDetectorRef, private subscriptionService : SubscriptionService) {

    super(cd);
    this.setService(subscriptionService);
    this.orderBy = 'id';
    this.orderDir = 'DESC';
  }
}
